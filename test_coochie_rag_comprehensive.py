#!/usr/bin/env python3
"""
Comprehensive RAG and AI Functionality Test using Coochie Information Pack PDF

This test script validates the complete RAG pipeline:
1. Document upload and processing
2. Vector embedding generation
3. Question answering with context retrieval
4. Agent system integration
5. Multi-agent workflow testing

Tests the Coochie Hydrogreen franchise information pack PDF through various endpoints.
"""

import asyncio
import os
import sys
import time
import requests
from pathlib import Path
from typing import Dict, Any, List, Optional
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configuration
BASE_URL = "http://localhost:8000/api"
LOGIN_EMAIL = "<EMAIL>"
LOGIN_PASSWORD = "Admin@1234"
PDF_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"

class CoochieRAGTester:
    """Comprehensive RAG system tester for Coochie Information Pack"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.token = None
        self.headers = {}
        self.document_id = None
        self.franchisor_id = None
        self.session_id = f"coochie_test_{int(time.time())}"
        
    def login(self) -> bool:
        """Authenticate and get access token"""
        print("🔐 Authenticating...")
        
        login_data = {
            "email_or_mobile": LOGIN_EMAIL,
            "password": LOGIN_PASSWORD,
            "remember_me": True
        }
        
        try:
            response = requests.post(f"{self.base_url}/auth/login", json=login_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.token = result["data"]["details"]["access_token"]
                    self.headers = {
                        "Authorization": f"Bearer {self.token}",
                        "Content-Type": "application/json"
                    }
                    print("✅ Authentication successful!")
                    return True
            
            print(f"❌ Authentication failed: {response.text}")
            return False
            
        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            return False
    
    def check_pdf_file(self) -> bool:
        """Verify the PDF file exists and is accessible"""
        print("📄 Checking PDF file...")
        
        if not os.path.exists(PDF_PATH):
            print(f"❌ PDF file not found: {PDF_PATH}")
            return False
        
        file_size = os.path.getsize(PDF_PATH)
        print(f"✅ PDF file found: {os.path.basename(PDF_PATH)}")
        print(f"   - Size: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
        
        return True
    
    def test_system_health(self) -> bool:
        """Test system health endpoints"""
        print("\n🏥 Testing system health...")
        
        # Test DocQA health
        try:
            response = requests.get(f"{self.base_url}/docqa/health", headers=self.headers)
            if response.status_code == 200:
                result = response.json()
                print("✅ DocQA system healthy")
                print(f"   - Status: {result.get('data', {}).get('status', 'Unknown')}")
            else:
                print(f"⚠️ DocQA health check failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ DocQA health check error: {str(e)}")
        
        # Test agent system status
        try:
            response = requests.get(f"{self.base_url}/agents/status", headers=self.headers)
            if response.status_code == 200:
                result = response.json()
                print("✅ Agent system healthy")
                print(f"   - Agents: {len(result.get('data', {}).get('agents', {}))}")
            else:
                print(f"⚠️ Agent status check failed: {response.status_code}")
        except Exception as e:
            print(f"⚠️ Agent status check error: {str(e)}")
        
        return True
    
    def upload_coochie_pdf(self) -> bool:
        """Upload the Coochie Information Pack PDF"""
        print("\n📤 Uploading Coochie Information Pack PDF...")
        
        try:
            # Prepare file upload
            with open(PDF_PATH, 'rb') as pdf_file:
                files = {
                    'file': ('Coochie_Information_pack.pdf', pdf_file, 'application/pdf')
                }
                
                data = {
                    'name': 'Coochie Hydrogreen Information Pack',
                    'description': 'Franchise information pack for Coochie Hydrogreen car wash franchise',
                    'is_active': True
                }
                
                # Remove Content-Type from headers for multipart upload
                upload_headers = {"Authorization": f"Bearer {self.token}"}
                
                response = requests.post(
                    f"{self.base_url}/documents/upload",
                    files=files,
                    data=data,
                    headers=upload_headers,
                    timeout=120
                )
            
            if response.status_code == 201:
                result = response.json()
                self.document_id = result["data"]["id"]
                print(f"✅ PDF uploaded successfully!")
                print(f"   - Document ID: {self.document_id}")
                print(f"   - Processing Status: {result['data'].get('processing_status', 'Unknown')}")
                print("⏳ DocQA processing initiated automatically...")
                return True
            else:
                print(f"❌ PDF upload failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ PDF upload error: {str(e)}")
            return False
    
    def wait_for_processing(self, timeout: int = 180) -> bool:
        """Wait for document processing to complete"""
        print(f"\n⏳ Waiting for document processing (timeout: {timeout}s)...")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get(
                    f"{self.base_url}/documents/{self.document_id}",
                    headers=self.headers
                )
                
                if response.status_code == 200:
                    result = response.json()
                    status = result["data"].get("processing_status", "unknown")
                    
                    print(f"   Processing status: {status}")
                    
                    if status == "completed":
                        print("✅ Document processing completed!")
                        return True
                    elif status == "failed":
                        print("❌ Document processing failed!")
                        return False
                    
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                print(f"⚠️ Error checking processing status: {str(e)}")
                time.sleep(5)
        
        print("⚠️ Processing timeout reached - continuing with tests")
        return True  # Continue even if we can't confirm completion
    
    def test_document_specific_questions(self) -> bool:
        """Test questions specific to the uploaded document"""
        print(f"\n❓ Testing document-specific questions...")
        
        test_questions = [
            {
                "question": "What are the investment requirements for Coochie Hydrogreen franchise?",
                "expected_keywords": ["investment", "cost", "fee", "capital"]
            },
            {
                "question": "What franchise fees does Coochie Hydrogreen charge?",
                "expected_keywords": ["franchise fee", "royalty", "percentage"]
            },
            {
                "question": "What support does Coochie Hydrogreen provide to franchisees?",
                "expected_keywords": ["support", "training", "assistance"]
            },
            {
                "question": "What are the territory requirements for Coochie Hydrogreen?",
                "expected_keywords": ["territory", "location", "area"]
            },
            {
                "question": "Is Coochie Hydrogreen an eco-friendly business?",
                "expected_keywords": ["eco", "environment", "green", "sustainable"]
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_questions, 1):
            print(f"\n🔍 Question {i}: {test_case['question']}")
            
            try:
                question_data = {
                    "question": test_case["question"],
                    "top_k": 5,
                    "similarity_threshold": 0.6,
                    "include_metadata": True
                }
                
                response = requests.post(
                    f"{self.base_url}/docqa/ask/document/{self.document_id}",
                    json=question_data,
                    headers=self.headers,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result["data"]["answer"]
                    
                    print(f"✅ Answer received:")
                    print(f"   {answer[:200]}...")
                    
                    # Check for expected keywords
                    answer_lower = answer.lower()
                    found_keywords = [kw for kw in test_case["expected_keywords"] if kw.lower() in answer_lower]
                    
                    if found_keywords:
                        print(f"✅ Found relevant keywords: {found_keywords}")
                    else:
                        print(f"⚠️ No expected keywords found in answer")
                    
                    results.append({
                        "question": test_case["question"],
                        "success": True,
                        "answer_length": len(answer),
                        "keywords_found": found_keywords
                    })
                else:
                    print(f"❌ Question failed: {response.status_code}")
                    print(f"   Response: {response.text}")
                    results.append({
                        "question": test_case["question"],
                        "success": False,
                        "error": response.text
                    })
                    
            except Exception as e:
                print(f"❌ Question error: {str(e)}")
                results.append({
                    "question": test_case["question"],
                    "success": False,
                    "error": str(e)
                })
            
            time.sleep(2)  # Brief pause between questions
        
        # Summary
        successful = sum(1 for r in results if r["success"])
        print(f"\n📊 Document Questions Summary: {successful}/{len(results)} successful")
        
        return successful > 0

    def test_general_rag_questions(self) -> bool:
        """Test general RAG questions that should find the document"""
        print(f"\n❓ Testing general RAG questions...")

        test_questions = [
            {
                "question": "What franchise opportunities are available?",
                "description": "General franchise inquiry"
            },
            {
                "question": "Tell me about eco-friendly car wash franchises",
                "description": "Contextual detection - eco-friendly + car wash"
            },
            {
                "question": "What are the costs for starting a car wash franchise?",
                "description": "Industry-specific cost inquiry"
            },
            {
                "question": "Which franchises don't require volatile tenancy fees?",
                "description": "Business model specific question"
            }
        ]

        results = []

        for i, test_case in enumerate(test_questions, 1):
            print(f"\n🔍 General Question {i}: {test_case['question']}")
            print(f"   Description: {test_case['description']}")

            try:
                question_data = {
                    "question": test_case["question"],
                    "top_k": 6,
                    "similarity_threshold": 0.7,
                    "include_metadata": True
                }

                response = requests.post(
                    f"{self.base_url}/docqa/ask",
                    json=question_data,
                    headers=self.headers,
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()
                    answer = result["data"]["answer"]

                    print(f"✅ Answer received:")
                    print(f"   {answer[:200]}...")

                    # Check if Coochie is mentioned
                    if "coochie" in answer.lower():
                        print("✅ Coochie Hydrogreen detected in answer")
                    else:
                        print("⚠️ Coochie Hydrogreen not explicitly mentioned")

                    results.append({
                        "question": test_case["question"],
                        "success": True,
                        "answer_length": len(answer),
                        "coochie_mentioned": "coochie" in answer.lower()
                    })
                else:
                    print(f"❌ Question failed: {response.status_code}")
                    results.append({
                        "question": test_case["question"],
                        "success": False,
                        "error": response.text
                    })

            except Exception as e:
                print(f"❌ Question error: {str(e)}")
                results.append({
                    "question": test_case["question"],
                    "success": False,
                    "error": str(e)
                })

            time.sleep(2)

        successful = sum(1 for r in results if r["success"])
        print(f"\n📊 General RAG Questions Summary: {successful}/{len(results)} successful")

        return successful > 0

    def test_agent_system_integration(self) -> bool:
        """Test the multi-agent system with Coochie-related queries"""
        print(f"\n🤖 Testing agent system integration...")

        agent_test_cases = [
            {
                "message": "Hello! I'm interested in franchise opportunities.",
                "description": "Initial greeting and interest",
                "expected_intent": "conversation"
            },
            {
                "message": "Tell me about Coochie Hydrogreen franchise investment requirements.",
                "description": "Specific franchise inquiry",
                "expected_intent": "question_answering"
            },
            {
                "message": "I want to apply for a Coochie Hydrogreen franchise.",
                "description": "Lead qualification trigger",
                "expected_intent": "lead_qualification"
            },
            {
                "message": "Can you schedule a meeting to discuss the franchise?",
                "description": "Meeting booking request",
                "expected_intent": "meeting_booking"
            }
        ]

        results = []

        for i, test_case in enumerate(agent_test_cases, 1):
            print(f"\n🤖 Agent Test {i}: {test_case['message']}")
            print(f"   Description: {test_case['description']}")
            print(f"   Expected Intent: {test_case['expected_intent']}")

            try:
                chat_data = {
                    "message": test_case["message"],
                    "session_id": self.session_id,
                    "context": {
                        "test_mode": True,
                        "document_id": self.document_id
                    }
                }

                response = requests.post(
                    f"{self.base_url}/agents/chat",
                    json=chat_data,
                    headers=self.headers,
                    timeout=60
                )

                if response.status_code == 200:
                    result = response.json()

                    print(f"✅ Agent response received:")
                    print(f"   Response: {result.get('response', 'No response')[:150]}...")
                    print(f"   Intent: {result.get('intent', 'Unknown')}")
                    print(f"   Success: {result.get('success', False)}")

                    results.append({
                        "message": test_case["message"],
                        "success": result.get("success", False),
                        "intent": result.get("intent"),
                        "response_length": len(result.get("response", "")),
                        "expected_intent": test_case["expected_intent"]
                    })
                else:
                    print(f"❌ Agent chat failed: {response.status_code}")
                    results.append({
                        "message": test_case["message"],
                        "success": False,
                        "error": response.text
                    })

            except Exception as e:
                print(f"❌ Agent chat error: {str(e)}")
                results.append({
                    "message": test_case["message"],
                    "success": False,
                    "error": str(e)
                })

            time.sleep(3)  # Longer pause for agent processing

        successful = sum(1 for r in results if r["success"])
        print(f"\n📊 Agent System Summary: {successful}/{len(results)} successful")

        return successful > 0

    def test_webhook_integration(self) -> bool:
        """Test webhook integration with SMS-like messages"""
        print(f"\n📱 Testing webhook integration...")

        webhook_test_cases = [
            {
                "message": "What are the investment requirements for Coochie Hydrogreen franchise?",
                "description": "Direct franchise inquiry via webhook"
            },
            {
                "message": "I'm interested in eco-friendly car wash business opportunities",
                "description": "Contextual inquiry via webhook"
            },
            {
                "message": "Tell me about franchise support and training",
                "description": "Support inquiry via webhook"
            }
        ]

        results = []

        for i, test_case in enumerate(webhook_test_cases, 1):
            print(f"\n📱 Webhook Test {i}: {test_case['message']}")
            print(f"   Description: {test_case['description']}")

            try:
                webhook_payload = {
                    "event_type": "SMS_INBOUND",
                    "timestamp": "2025-07-13T16:30:00Z",
                    "mo": {
                        "id": f"test-msg-{int(time.time())}-{i}",
                        "sender": "***********",
                        "recipient": "***********",
                        "message": test_case["message"]
                    }
                }

                response = requests.post(
                    f"{self.base_url}/webhooks/webhooks/kudosity",
                    json=webhook_payload,
                    headers=self.headers,
                    timeout=60
                )

                if response.status_code == 200:
                    print("✅ Webhook processed successfully")
                    print("   (Check server logs for AI response)")

                    results.append({
                        "message": test_case["message"],
                        "success": True,
                        "status_code": 200
                    })
                else:
                    print(f"❌ Webhook failed: {response.status_code}")
                    results.append({
                        "message": test_case["message"],
                        "success": False,
                        "status_code": response.status_code,
                        "error": response.text
                    })

            except Exception as e:
                print(f"❌ Webhook error: {str(e)}")
                results.append({
                    "message": test_case["message"],
                    "success": False,
                    "error": str(e)
                })

            time.sleep(2)

        successful = sum(1 for r in results if r["success"])
        print(f"\n📊 Webhook Integration Summary: {successful}/{len(results)} successful")

        return successful > 0

    def test_edge_cases(self) -> bool:
        """Test edge cases and error handling"""
        print(f"\n⚠️ Testing edge cases...")

        edge_test_cases = [
            {
                "question": "What's the weather like today?",
                "description": "Non-franchise question",
                "endpoint": "docqa/ask"
            },
            {
                "question": "Tell me about XYZ Franchise that doesn't exist",
                "description": "Non-existent franchise",
                "endpoint": "docqa/ask"
            },
            {
                "question": "",
                "description": "Empty question",
                "endpoint": "docqa/ask"
            }
        ]

        results = []

        for i, test_case in enumerate(edge_test_cases, 1):
            print(f"\n⚠️ Edge Case {i}: {test_case['description']}")
            print(f"   Question: '{test_case['question']}'")

            try:
                question_data = {
                    "question": test_case["question"],
                    "top_k": 5,
                    "similarity_threshold": 0.7
                }

                response = requests.post(
                    f"{self.base_url}/{test_case['endpoint']}",
                    json=question_data,
                    headers=self.headers,
                    timeout=30
                )

                if response.status_code in [200, 400, 422]:  # Expected responses
                    print(f"✅ Handled appropriately: {response.status_code}")

                    if response.status_code == 200:
                        result = response.json()
                        answer = result["data"]["answer"]
                        print(f"   Answer: {answer[:100]}...")

                    results.append({
                        "question": test_case["question"],
                        "success": True,
                        "status_code": response.status_code
                    })
                else:
                    print(f"⚠️ Unexpected status: {response.status_code}")
                    results.append({
                        "question": test_case["question"],
                        "success": False,
                        "status_code": response.status_code
                    })

            except Exception as e:
                print(f"❌ Edge case error: {str(e)}")
                results.append({
                    "question": test_case["question"],
                    "success": False,
                    "error": str(e)
                })

            time.sleep(1)

        successful = sum(1 for r in results if r["success"])
        print(f"\n📊 Edge Cases Summary: {successful}/{len(results)} handled appropriately")

        return successful > 0

    def generate_test_report(self, test_results: Dict[str, bool]) -> None:
        """Generate comprehensive test report"""
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE RAG TEST REPORT")
        print("="*80)

        total_tests = len(test_results)
        passed_tests = sum(1 for result in test_results.values() if result)
        failed_tests = total_tests - passed_tests

        print(f"📈 Overall Results:")
        print(f"   Total Test Suites: {total_tests}")
        print(f"   Passed: {passed_tests}")
        print(f"   Failed: {failed_tests}")
        print(f"   Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        print(f"\n📋 Test Suite Results:")
        for test_name, result in test_results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"   {test_name}: {status}")

        print(f"\n📄 Document Information:")
        print(f"   PDF File: {os.path.basename(PDF_PATH)}")
        print(f"   Document ID: {self.document_id}")
        print(f"   Session ID: {self.session_id}")

        print(f"\n🎯 Key Features Tested:")
        print("   ✓ PDF document upload and processing")
        print("   ✓ Vector embedding generation")
        print("   ✓ Document-specific question answering")
        print("   ✓ General RAG question answering")
        print("   ✓ Multi-agent system integration")
        print("   ✓ Webhook integration")
        print("   ✓ Edge case handling")

        if passed_tests == total_tests:
            print("\n🎉 ALL TESTS PASSED! The RAG system is working correctly.")
        else:
            print(f"\n⚠️ {failed_tests} test suite(s) failed. Check the output above for details.")

        print("\n💡 Next Steps:")
        print("   - Check server logs for detailed AI responses")
        print("   - Verify document processing in the database")
        print("   - Test with additional PDF documents")
        print("   - Monitor system performance under load")

    async def run_comprehensive_test(self) -> bool:
        """Run the complete comprehensive test suite"""
        print("🚀 COMPREHENSIVE RAG AND AI FUNCTIONALITY TEST")
        print("="*80)
        print("Testing the complete RAG pipeline with Coochie Information Pack PDF")
        print("="*80)

        test_results = {}

        # Step 1: Authentication
        print("\n🔐 Step 1: Authentication")
        if not self.login():
            print("❌ Authentication failed - cannot continue")
            return False

        # Step 2: File validation
        print("\n📄 Step 2: File Validation")
        if not self.check_pdf_file():
            print("❌ PDF file validation failed - cannot continue")
            return False

        # Step 3: System health check
        print("\n🏥 Step 3: System Health Check")
        test_results["System Health"] = self.test_system_health()

        # Step 4: Document upload
        print("\n📤 Step 4: Document Upload")
        if not self.upload_coochie_pdf():
            print("❌ Document upload failed - cannot continue")
            return False

        # Step 5: Wait for processing
        print("\n⏳ Step 5: Processing Wait")
        self.wait_for_processing()

        # Step 6: Document-specific questions
        print("\n❓ Step 6: Document-Specific Questions")
        test_results["Document Questions"] = self.test_document_specific_questions()

        # Step 7: General RAG questions
        print("\n🔍 Step 7: General RAG Questions")
        test_results["General RAG"] = self.test_general_rag_questions()

        # Step 8: Agent system integration
        print("\n🤖 Step 8: Agent System Integration")
        test_results["Agent System"] = self.test_agent_system_integration()

        # Step 9: Webhook integration
        print("\n📱 Step 9: Webhook Integration")
        test_results["Webhook Integration"] = self.test_webhook_integration()

        # Step 10: Edge cases
        print("\n⚠️ Step 10: Edge Cases")
        test_results["Edge Cases"] = self.test_edge_cases()

        # Generate comprehensive report
        self.generate_test_report(test_results)

        # Return overall success
        return all(test_results.values())


def main():
    """Main execution function"""
    print("🧪 Coochie RAG Comprehensive Test Suite")
    print("This script tests the complete RAG and AI functionality using the Coochie Information Pack PDF")
    print()

    # Check if PDF file exists before starting
    if not os.path.exists(PDF_PATH):
        print(f"❌ PDF file not found: {PDF_PATH}")
        print("Please ensure the Coochie Information pack.pdf file is in the correct location.")
        return False

    # Initialize and run tester
    tester = CoochieRAGTester()

    try:
        success = asyncio.run(tester.run_comprehensive_test())

        if success:
            print("\n🎯 Comprehensive test completed successfully!")
            print("The RAG and AI functionality is working correctly with the Coochie Information Pack.")
        else:
            print("\n❌ Some tests failed!")
            print("Please check the output above and server logs for details.")

        return success

    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)
