
"""
LayoutParser PyTorch Compatibility Wrapper

This module provides a compatibility layer for LayoutParser with PyTorch 2.7+
"""

import torch
import layoutparser as lp
from functools import wraps
import warnings

# Store original torch.load
_original_torch_load = torch.load

def _patched_torch_load(*args, **kwargs):
    """Patched torch.load that sets weights_only=False for compatibility"""
    if 'weights_only' not in kwargs:
        kwargs['weights_only'] = False
    return _original_torch_load(*args, **kwargs)

# Monkey patch torch.load
torch.load = _patched_torch_load

# Re-export LayoutParser components
Detectron2LayoutModel = lp.Detectron2LayoutModel
TesseractAgent = lp.TesseractAgent

# Convenience function for creating models
def create_layout_model(model_name='PubLayNet', confidence_threshold=0.8):
    """
    Create a LayoutParser model with proper configuration
    
    Args:
        model_name: Model name ('PubLayNet', 'TableBank', 'NewspaperNavigator')
        confidence_threshold: Confidence threshold for detection
        
    Returns:
        LayoutParser model instance
    """
    
    model_configs = {
        'PubLayNet': {
            'config': 'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
            'labels': {0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
        },
        'TableBank': {
            'config': 'lp://TableBank/faster_rcnn_R_50_FPN_3x/config',
            'labels': {0: "Table"}
        },
        'NewspaperNavigator': {
            'config': 'lp://NewspaperNavigator/faster_rcnn_R_50_FPN_3x/config',
            'labels': {0: "Photograph", 1: "Illustration", 2: "Map", 3: "Comics/Cartoon", 4: "Editorial Cartoon", 5: "Headline", 6: "Advertisement"}
        }
    }
    
    if model_name not in model_configs:
        raise ValueError(f"Unknown model: {model_name}. Available: {list(model_configs.keys())}")
    
    config = model_configs[model_name]
    
    return lp.Detectron2LayoutModel(
        config['config'],
        extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", confidence_threshold],
        label_map=config['labels']
    )

# Example usage function
def process_document_with_layoutparser(image_path, model_name='PubLayNet'):
    """
    Process a document image with LayoutParser
    
    Args:
        image_path: Path to the image file
        model_name: Model to use for layout detection
        
    Returns:
        List of detected layout elements
    """
    
    # Load model
    model = create_layout_model(model_name)
    
    # Load image
    if hasattr(lp, 'io'):
        image = lp.io.load_image(image_path)
    else:
        from PIL import Image
        image = Image.open(image_path)
    
    # Detect layout
    layout = model.detect(image)
    
    return layout

print("✅ LayoutParser compatibility wrapper loaded")
print("💡 Use: import layoutparser_compat as lp")
