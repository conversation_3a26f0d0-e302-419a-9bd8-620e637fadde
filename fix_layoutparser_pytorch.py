#!/usr/bin/env python3
"""
Fix LayoutParser PyTorch Compatibility

This script fixes the PyTorch 2.7+ compatibility issue with LayoutParser
by patching the torch.load calls to use weights_only=False.
"""

import os
import sys
from pathlib import Path

def patch_detectron2_checkpoint():
    """Patch Detectron2 checkpoint loading for PyTorch 2.7+ compatibility"""
    
    # Find the detectron2 installation
    try:
        import detectron2
        detectron2_path = Path(detectron2.__file__).parent
        checkpoint_file = detectron2_path / "checkpoint" / "detection_checkpoint.py"
        
        if not checkpoint_file.exists():
            print(f"❌ Detectron2 checkpoint file not found: {checkpoint_file}")
            return False
        
        print(f"📝 Patching Detectron2 checkpoint file: {checkpoint_file}")
        
        # Read the current file
        with open(checkpoint_file, 'r') as f:
            content = f.read()
        
        # Check if already patched
        if 'weights_only=False' in content:
            print("✅ Detectron2 already patched")
            return True
        
        # Apply the patch
        original_line = 'return torch.load(f, map_location=torch.device("cpu"))'
        patched_line = 'return torch.load(f, map_location=torch.device("cpu"), weights_only=False)'
        
        if original_line in content:
            content = content.replace(original_line, patched_line)
            
            # Write the patched file
            with open(checkpoint_file, 'w') as f:
                f.write(content)
            
            print("✅ Detectron2 checkpoint patched successfully")
            return True
        else:
            print("⚠️  Could not find the line to patch in Detectron2")
            return False
            
    except Exception as e:
        print(f"❌ Failed to patch Detectron2: {e}")
        return False

def patch_fvcore_checkpoint():
    """Patch fvcore checkpoint loading for PyTorch 2.7+ compatibility"""
    
    try:
        import fvcore
        fvcore_path = Path(fvcore.__file__).parent
        checkpoint_file = fvcore_path / "common" / "checkpoint.py"
        
        if not checkpoint_file.exists():
            print(f"❌ fvcore checkpoint file not found: {checkpoint_file}")
            return False
        
        print(f"📝 Patching fvcore checkpoint file: {checkpoint_file}")
        
        # Read the current file
        with open(checkpoint_file, 'r') as f:
            content = f.read()
        
        # Check if already patched
        if 'weights_only=False' in content:
            print("✅ fvcore already patched")
            return True
        
        # Apply the patch
        original_line = 'return torch.load(f, map_location=torch.device("cpu"))'
        patched_line = 'return torch.load(f, map_location=torch.device("cpu"), weights_only=False)'
        
        if original_line in content:
            content = content.replace(original_line, patched_line)
            
            # Write the patched file
            with open(checkpoint_file, 'w') as f:
                f.write(content)
            
            print("✅ fvcore checkpoint patched successfully")
            return True
        else:
            print("⚠️  Could not find the line to patch in fvcore")
            return False
            
    except Exception as e:
        print(f"❌ Failed to patch fvcore: {e}")
        return False

def test_layoutparser_after_patch():
    """Test LayoutParser functionality after patching"""
    print("\n🧪 Testing LayoutParser after patching...")
    
    try:
        import layoutparser as lp
        import numpy as np
        from PIL import Image
        
        # Create a simple test image
        test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255
        test_image[50:150, 50:550] = [0, 0, 0]  # Title area
        test_image[200:350, 50:550] = [128, 128, 128]  # Text area
        
        # Convert to PIL Image
        pil_image = Image.fromarray(test_image)
        
        print("📥 Loading LayoutParser model...")
        
        # Load model with patched checkpoint loading
        model = lp.Detectron2LayoutModel(
            'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
            extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
            label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
        )
        
        print("✅ Model loaded successfully!")
        
        # Test layout detection
        print("🔍 Testing layout detection...")
        layout = model.detect(pil_image)
        
        print(f"✅ Layout detection successful!")
        print(f"   Detected {len(layout)} layout elements")
        
        for i, element in enumerate(layout):
            print(f"   Element {i+1}: {element.type} (confidence: {element.score:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ LayoutParser test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_layoutparser_wrapper():
    """Create a wrapper module that handles the PyTorch compatibility"""
    
    wrapper_code = '''
"""
LayoutParser PyTorch Compatibility Wrapper

This module provides a compatibility layer for LayoutParser with PyTorch 2.7+
"""

import torch
import layoutparser as lp
from functools import wraps
import warnings

# Store original torch.load
_original_torch_load = torch.load

def _patched_torch_load(*args, **kwargs):
    """Patched torch.load that sets weights_only=False for compatibility"""
    if 'weights_only' not in kwargs:
        kwargs['weights_only'] = False
    return _original_torch_load(*args, **kwargs)

# Monkey patch torch.load
torch.load = _patched_torch_load

# Re-export LayoutParser components
Detectron2LayoutModel = lp.Detectron2LayoutModel
TesseractAgent = lp.TesseractAgent

# Convenience function for creating models
def create_layout_model(model_name='PubLayNet', confidence_threshold=0.8):
    """
    Create a LayoutParser model with proper configuration
    
    Args:
        model_name: Model name ('PubLayNet', 'TableBank', 'NewspaperNavigator')
        confidence_threshold: Confidence threshold for detection
        
    Returns:
        LayoutParser model instance
    """
    
    model_configs = {
        'PubLayNet': {
            'config': 'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
            'labels': {0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
        },
        'TableBank': {
            'config': 'lp://TableBank/faster_rcnn_R_50_FPN_3x/config',
            'labels': {0: "Table"}
        },
        'NewspaperNavigator': {
            'config': 'lp://NewspaperNavigator/faster_rcnn_R_50_FPN_3x/config',
            'labels': {0: "Photograph", 1: "Illustration", 2: "Map", 3: "Comics/Cartoon", 4: "Editorial Cartoon", 5: "Headline", 6: "Advertisement"}
        }
    }
    
    if model_name not in model_configs:
        raise ValueError(f"Unknown model: {model_name}. Available: {list(model_configs.keys())}")
    
    config = model_configs[model_name]
    
    return lp.Detectron2LayoutModel(
        config['config'],
        extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", confidence_threshold],
        label_map=config['labels']
    )

# Example usage function
def process_document_with_layoutparser(image_path, model_name='PubLayNet'):
    """
    Process a document image with LayoutParser
    
    Args:
        image_path: Path to the image file
        model_name: Model to use for layout detection
        
    Returns:
        List of detected layout elements
    """
    
    # Load model
    model = create_layout_model(model_name)
    
    # Load image
    if hasattr(lp, 'io'):
        image = lp.io.load_image(image_path)
    else:
        from PIL import Image
        image = Image.open(image_path)
    
    # Detect layout
    layout = model.detect(image)
    
    return layout

print("✅ LayoutParser compatibility wrapper loaded")
print("💡 Use: import layoutparser_compat as lp")
'''
    
    # Write the wrapper
    with open("layoutparser_compat.py", "w") as f:
        f.write(wrapper_code)
    
    print("✅ LayoutParser compatibility wrapper created: layoutparser_compat.py")

def main():
    """Main function to fix LayoutParser PyTorch compatibility"""
    
    print("🔧 LayoutParser PyTorch Compatibility Fix")
    print("=" * 50)
    
    # Check if we're in the virtual environment
    if 'lp-env' not in sys.prefix:
        print("⚠️  Please activate the lp-env virtual environment first:")
        print("   source lp-env/bin/activate")
        sys.exit(1)
    
    print("✅ Virtual environment detected")
    
    # Patch the checkpoint loading
    print("\n🔧 Applying compatibility patches...")
    
    detectron2_patched = patch_detectron2_checkpoint()
    fvcore_patched = patch_fvcore_checkpoint()
    
    if detectron2_patched and fvcore_patched:
        print("\n✅ All patches applied successfully")
        
        # Test LayoutParser
        if test_layoutparser_after_patch():
            print("\n🎉 LayoutParser is now working correctly!")
            
            # Create compatibility wrapper
            create_layoutparser_wrapper()
            
            print("\n📋 Usage Instructions:")
            print("1. Use the patched LayoutParser directly:")
            print("   import layoutparser as lp")
            print("   model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')")
            print("")
            print("2. Or use the compatibility wrapper:")
            print("   import layoutparser_compat as lp")
            print("   model = lp.create_layout_model('PubLayNet')")
            print("")
            print("3. Process documents:")
            print("   layout = lp.process_document_with_layoutparser('document.png')")
            
        else:
            print("\n❌ LayoutParser test failed after patching")
            
    else:
        print("\n❌ Some patches failed. LayoutParser may not work correctly.")
        
        # Create compatibility wrapper anyway
        create_layoutparser_wrapper()
        print("\n💡 Try using the compatibility wrapper: import layoutparser_compat as lp")

if __name__ == "__main__":
    main()
