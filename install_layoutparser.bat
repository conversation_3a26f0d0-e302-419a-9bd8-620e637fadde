@echo off
REM LayoutParser Installation Script for Windows
REM This script installs LayoutParser with all dependencies in a clean virtual environment

echo 🚀 LayoutParser Installation Script for Windows
echo ================================================

REM Check Python version
echo 🐍 Checking Python version...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Create virtual environment
echo 📦 Creating virtual environment 'lp-env'...
if exist lp-env (
    echo ⚠️  Virtual environment 'lp-env' already exists. Removing...
    rmdir /s /q lp-env
)

python -m venv lp-env

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call lp-env\Scripts\activate.bat

REM Upgrade pip
echo ⬆️  Upgrading pip...
python -m pip install --upgrade pip setuptools wheel

REM Check for system dependencies
echo 📋 Checking system dependencies...
tesseract --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Tesseract not found. Please install:
    echo    1. Download from: https://github.com/tesseract-ocr/tesseract
    echo    2. Install and add to PATH
    echo    3. Restart this script
    pause
    exit /b 1
)

echo ✅ Tesseract found

REM Install PyTorch
echo 🔥 Installing PyTorch...
pip install torch torchvision torchaudio

REM Install Detectron2 (Windows-specific)
echo 🎯 Installing Detectron2...
pip install detectron2 -f https://dl.fbaipublicfiles.com/detectron2/wheels/cpu/torch1.10/index.html

REM Install LayoutParser
echo 📐 Installing LayoutParser...
pip install "layoutparser[layoutmodels]"

REM Install additional dependencies
echo 📚 Installing additional dependencies...
pip install pytesseract pdf2image opencv-python pillow numpy

REM Create verification script
echo 📝 Creating verification script...
(
echo import sys
echo import traceback
echo.
echo def test_imports^(^):
echo     """Test basic imports"""
echo     print^("🧪 Testing imports..."^)
echo     
echo     try:
echo         import layoutparser as lp
echo         print^("✅ LayoutParser imported successfully"^)
echo         print^(f"   Version: {lp.__version__}"^)
echo     except ImportError as e:
echo         print^(f"❌ LayoutParser import failed: {e}"^)
echo         return False
echo     
echo     try:
echo         import detectron2
echo         print^("✅ Detectron2 imported successfully"^)
echo     except ImportError as e:
echo         print^(f"❌ Detectron2 import failed: {e}"^)
echo         return False
echo     
echo     try:
echo         import torch
echo         print^("✅ PyTorch imported successfully"^)
echo         print^(f"   Version: {torch.__version__}"^)
echo     except ImportError as e:
echo         print^(f"❌ PyTorch import failed: {e}"^)
echo         return False
echo     
echo     return True
echo.
echo def test_model_loading^(^):
echo     """Test loading a LayoutParser model"""
echo     print^("🎯 Testing model loading..."^)
echo     
echo     try:
echo         import layoutparser as lp
echo         model = lp.Detectron2LayoutModel^('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config'^)
echo         print^("✅ LayoutParser model loaded successfully"^)
echo         return True
echo     except Exception as e:
echo         print^(f"❌ Model loading failed: {e}"^)
echo         return False
echo.
echo def main^(^):
echo     print^("🧪 LayoutParser Installation Verification"^)
echo     print^("=" * 50^)
echo     
echo     if not test_imports^(^):
echo         print^("❌ Import tests failed"^)
echo         sys.exit^(1^)
echo     
echo     if not test_model_loading^(^):
echo         print^("⚠️  Model loading failed"^)
echo     
echo     print^("🎉 LayoutParser verification completed!"^)
echo.
echo if __name__ == "__main__":
echo     main^(^)
) > verify_layoutparser.py

REM Run verification
echo 🧪 Running verification tests...
python verify_layoutparser.py

echo.
echo 🎉 LayoutParser installation completed!
echo.
echo 📋 Summary:
echo    ✅ Virtual environment 'lp-env' created
echo    ✅ LayoutParser with layout models installed
echo    ✅ Detectron2 installed
echo    ✅ PyTorch installed
echo.
echo 🚀 To use LayoutParser:
echo    1. Activate environment: lp-env\Scripts\activate.bat
echo    2. Run your Python scripts with LayoutParser
echo.
echo 📖 Example usage:
echo    import layoutparser as lp
echo    model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')
echo    layout = model.detect(image)
echo.
pause
