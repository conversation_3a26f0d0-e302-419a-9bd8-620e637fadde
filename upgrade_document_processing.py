#!/usr/bin/env python3
"""
Upgrade Document Processing to Enhanced Version

This script upgrades the existing document processing system to use
the new advanced capabilities including LayoutParser, multiple OCR engines,
and AI-powered content analysis.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from docqa.advanced_ingestion.integration_service import (
    create_enhanced_integration_service,
    process_document_with_enhanced_capabilities
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        'layoutparser',
        'detectron2',
        'pytesseract',
        'easyocr',
        'opencv-python',
        'camelot-py',
        'tabula-py',
        'pdfplumber',
        'spacy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\nInstall missing packages with:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required packages are installed")
    return True

def check_system_dependencies():
    """Check system dependencies"""
    dependencies = {
        'tesseract': 'tesseract --version',
        'java': 'java -version'  # Required for tabula-py
    }
    
    missing_deps = []
    
    for dep, command in dependencies.items():
        try:
            os.system(f"{command} > /dev/null 2>&1")
        except:
            missing_deps.append(dep)
    
    if missing_deps:
        print("❌ Missing system dependencies:")
        for dep in missing_deps:
            print(f"   - {dep}")
        print("\nInstall system dependencies:")
        print("   Ubuntu/Debian: sudo apt-get install tesseract-ocr default-jre")
        print("   macOS: brew install tesseract openjdk")
        return False
    
    print("✅ All system dependencies are available")
    return True

def check_environment_variables():
    """Check required environment variables"""
    required_vars = ['OPENAI_API_KEY', 'DATABASE_URL']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ Missing required environment variables:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    
    print("✅ All required environment variables are set")
    return True

async def test_enhanced_processing():
    """Test the enhanced processing system"""
    print("\n🧪 Testing Enhanced Document Processing...")
    
    try:
        # Create integration service
        service = create_enhanced_integration_service()
        
        # Get capabilities
        capabilities = service.get_processing_capabilities()
        print("✅ Enhanced processing service created successfully")
        print(f"   Features: {capabilities['features']}")
        
        # Test with Coochie Information Pack if available
        test_file = "Coochie_Information pack.pdf"
        
        if Path(test_file).exists():
            print(f"\n📄 Testing with: {test_file}")
            
            result = await process_document_with_enhanced_capabilities(test_file)
            
            if result['success']:
                print("✅ Enhanced processing test successful!")
                print(f"   Chunks created: {result['chunks_created']}")
                print(f"   Layout elements detected: {result['enhanced_features']['layout_elements_detected']}")
                print(f"   Images processed: {result['enhanced_features']['images_processed']}")
                print(f"   Tables extracted: {result['enhanced_features']['tables_extracted']}")
                print(f"   Charts analyzed: {result['enhanced_features']['charts_analyzed']}")
                print(f"   Processing time: {result['processing_time']:.2f}s")
                return True
            else:
                print(f"❌ Enhanced processing test failed: {result['error_message']}")
                return False
        else:
            print(f"⚠️ Test file not found: {test_file}")
            print("   Enhanced processing service is ready but cannot test without a document")
            return True
            
    except Exception as e:
        print(f"❌ Enhanced processing test failed: {str(e)}")
        return False

def create_requirements_file():
    """Create requirements file for enhanced processing"""
    requirements = """
# Enhanced Document Processing Requirements

# Core document processing
PyMuPDF>=1.23.0
pdfplumber>=0.9.0
camelot-py[cv]>=0.10.1
tabula-py>=2.8.0

# Layout analysis
layoutparser[layoutmodels,tesseract,paddledetection]>=0.3.4
detectron2>=0.6

# OCR engines
pytesseract>=0.3.10
easyocr>=1.7.0
paddleocr>=2.7.0

# Image processing
opencv-python>=4.8.0
Pillow>=10.0.0

# AI and NLP
openai>=1.0.0
spacy>=3.7.0
transformers>=4.30.0
torch>=2.0.0
torchvision>=0.15.0

# Database and async
asyncpg>=0.28.0
aiofiles>=23.0.0
aiohttp>=3.8.0

# Utilities
numpy>=1.24.0
pandas>=2.0.0
"""
    
    with open("requirements_enhanced.txt", "w") as f:
        f.write(requirements.strip())
    
    print("✅ Created requirements_enhanced.txt")

def create_installation_script():
    """Create installation script for enhanced processing"""
    script = """#!/bin/bash
# Enhanced Document Processing Installation Script

echo "🚀 Installing Enhanced Document Processing System..."

# Install system dependencies
echo "📦 Installing system dependencies..."
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    sudo apt-get update
    sudo apt-get install -y tesseract-ocr tesseract-ocr-eng default-jre
    sudo apt-get install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1
elif [[ "$OSTYPE" == "darwin"* ]]; then
    brew install tesseract openjdk
fi

# Install Python packages
echo "🐍 Installing Python packages..."
pip install -r requirements_enhanced.txt

# Download spaCy model
echo "📚 Downloading spaCy model..."
python -m spacy download en_core_web_sm

# Install LayoutParser models (optional)
echo "🎯 Installing LayoutParser models..."
python -c "import layoutparser as lp; lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')"

echo "✅ Enhanced Document Processing System installed successfully!"
echo ""
echo "🔧 Next steps:"
echo "1. Set environment variables: OPENAI_API_KEY, DATABASE_URL"
echo "2. Test the system: python upgrade_document_processing.py --test"
echo "3. Update your application to use the enhanced processing"
"""
    
    with open("install_enhanced_processing.sh", "w") as f:
        f.write(script)
    
    os.chmod("install_enhanced_processing.sh", 0o755)
    print("✅ Created install_enhanced_processing.sh")

def show_integration_instructions():
    """Show instructions for integrating enhanced processing"""
    instructions = """
🔧 INTEGRATION INSTRUCTIONS

To integrate enhanced document processing into your application:

1. **Replace Document Processing Calls**
   
   OLD:
   ```python
   from docqa.services.document_service import DocumentService
   result = await document_service.process_document(file_path)
   ```
   
   NEW:
   ```python
   from docqa.advanced_ingestion.integration_service import process_document_with_enhanced_capabilities
   result = await process_document_with_enhanced_capabilities(file_path)
   ```

2. **Update Document Upload Endpoint**
   
   In `app/api/v1/endpoints/documents.py`, replace the processing call:
   ```python
   # Replace existing processing with enhanced version
   from docqa.advanced_ingestion.integration_service import get_enhanced_integration_service
   
   service = await get_enhanced_integration_service()
   result = await service.process_document_enhanced(file_path, document_id)
   ```

3. **Update Agent Document Ingestion**
   
   In `app/agents/document_ingestion.py`, use enhanced processing:
   ```python
   from docqa.advanced_ingestion.integration_service import process_document_with_enhanced_capabilities
   
   result = await process_document_with_enhanced_capabilities(file_path)
   ```

4. **Environment Configuration**
   
   Add to your .env file:
   ```
   USE_LAYOUTPARSER=true
   USE_MULTIPLE_OCR=true
   USE_AI_ANALYSIS=true
   PARALLEL_PROCESSING=true
   MAX_WORKERS=4
   PROCESSING_TIMEOUT=600
   GPU_ACCELERATION=false
   ```

5. **Benefits of Enhanced Processing**
   
   - ✅ Layout-aware document understanding
   - ✅ Multiple OCR engines for better text extraction
   - ✅ AI-powered chart and image analysis
   - ✅ Semantic chunking for better context
   - ✅ Enhanced metadata extraction
   - ✅ Better question answering accuracy

6. **Testing**
   
   Test with your documents to ensure improved accuracy:
   ```bash
   python upgrade_document_processing.py --test
   ```
"""
    
    print(instructions)

async def main():
    """Main function"""
    print("🚀 Enhanced Document Processing Upgrade")
    print("=" * 50)
    
    # Parse command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == '--test':
        # Run test only
        if not check_environment_variables():
            sys.exit(1)
        
        success = await test_enhanced_processing()
        sys.exit(0 if success else 1)
    
    # Full upgrade process
    print("1. Checking requirements...")
    if not check_requirements():
        print("\n💡 Run: pip install -r requirements_enhanced.txt")
        create_requirements_file()
        create_installation_script()
        print("💡 Or run: ./install_enhanced_processing.sh")
        sys.exit(1)
    
    print("\n2. Checking system dependencies...")
    if not check_system_dependencies():
        sys.exit(1)
    
    print("\n3. Checking environment variables...")
    if not check_environment_variables():
        sys.exit(1)
    
    print("\n4. Testing enhanced processing...")
    success = await test_enhanced_processing()
    
    if success:
        print("\n🎉 Enhanced Document Processing is ready!")
        show_integration_instructions()
        
        print("\n📝 Files created:")
        print("   - requirements_enhanced.txt")
        print("   - install_enhanced_processing.sh")
        print("   - docqa/advanced_ingestion/ (enhanced processing modules)")
        
        print("\n🚀 Your document processing is now significantly more powerful!")
        print("   It can now handle complex layouts, extract tables and charts,")
        print("   perform advanced OCR, and provide much better question answering.")
        
    else:
        print("\n❌ Enhanced processing setup failed. Check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
