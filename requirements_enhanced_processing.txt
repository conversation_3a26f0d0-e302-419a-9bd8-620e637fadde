# Enhanced Document Processing Requirements
# Install these packages for advanced document processing capabilities

# Core document processing libraries
PyMuPDF>=1.23.0                    # Advanced PDF processing
pdfplumber>=0.9.0                  # Table extraction and layout analysis
camelot-py[cv]>=0.10.1            # Advanced table extraction
tabula-py>=2.8.0                   # Java-based table extraction

# Layout analysis and document understanding
layoutparser[layoutmodels,tesseract,paddledetection]>=0.3.4  # Document layout analysis
detectron2>=0.6                    # Deep learning for layout detection

# OCR engines for text extraction
pytesseract>=0.3.10               # Tesseract OCR wrapper
easyocr>=1.7.0                    # Easy-to-use OCR with multiple languages
paddleocr>=2.7.0                  # PaddlePaddle OCR (optional)

# Image processing and computer vision
opencv-python>=4.8.0              # Computer vision library
Pillow>=10.0.0                    # Python Imaging Library
scikit-image>=0.21.0              # Image processing algorithms

# AI and machine learning
openai>=1.0.0                     # OpenAI API for GPT-4 Vision and text analysis
transformers>=4.30.0              # Hugging Face transformers
torch>=2.0.0                      # PyTorch for deep learning
torchvision>=0.15.0               # Computer vision models

# Natural language processing
spacy>=3.7.0                      # Advanced NLP library
nltk>=3.8.0                       # Natural Language Toolkit

# Async and web libraries
asyncio>=3.4.3                    # Asynchronous programming
aiofiles>=23.0.0                  # Async file operations
aiohttp>=3.8.0                    # Async HTTP client

# Database and data processing
asyncpg>=0.28.0                   # Async PostgreSQL driver
pandas>=2.0.0                     # Data manipulation
numpy>=1.24.0                     # Numerical computing

# Utilities and helpers
tqdm>=4.65.0                      # Progress bars
python-dotenv>=1.0.0              # Environment variable management
pathlib>=1.0.1                    # Path manipulation
dataclasses>=0.6                  # Data classes (Python 3.7+)

# Optional: GPU acceleration (uncomment if you have CUDA)
# torch-audio>=2.0.0
# torch-text>=0.15.0

# Development and testing
pytest>=7.4.0                     # Testing framework
pytest-asyncio>=0.21.0            # Async testing support
