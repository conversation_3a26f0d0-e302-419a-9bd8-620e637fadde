
"""
Enhanced Document Processor with LayoutParser Integration

This module integrates LayoutParser into the enhanced document processing pipeline
with graceful fallback when LayoutParser is not available.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import numpy as np
from PIL import Image

logger = logging.getLogger(__name__)

class LayoutParserProcessor:
    """LayoutParser-based layout detection processor"""
    
    def __init__(self, model_name: str = 'PubLayNet', confidence_threshold: float = 0.8):
        self.model_name = model_name
        self.confidence_threshold = confidence_threshold
        self.model = None
        self.available = False
        
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize LayoutParser model with error handling"""
        try:
            import layoutparser as lp
            
            # Model configurations
            model_configs = {
                'PubLayNet': {
                    'config': 'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
                    'labels': {0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
                },
                'TableBank': {
                    'config': 'lp://TableBank/faster_rcnn_R_50_FPN_3x/config',
                    'labels': {0: "Table"}
                }
            }
            
            if self.model_name not in model_configs:
                logger.warning(f"Unknown model {self.model_name}, using PubLayNet")
                self.model_name = 'PubLayNet'
            
            config = model_configs[self.model_name]
            
            # Try to load the model
            self.model = lp.Detectron2LayoutModel(
                config['config'],
                extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", self.confidence_threshold],
                label_map=config['labels']
            )
            
            self.available = True
            logger.info(f"LayoutParser {self.model_name} model loaded successfully")
            
        except Exception as e:
            logger.warning(f"LayoutParser initialization failed: {e}")
            self.available = False
    
    def detect_layout(self, image: Image.Image) -> List[Dict[str, Any]]:
        """
        Detect layout elements in an image
        
        Args:
            image: PIL Image
            
        Returns:
            List of layout elements with type, bbox, and confidence
        """
        if not self.available:
            logger.warning("LayoutParser not available, using fallback")
            return self._fallback_layout_detection(image)
        
        try:
            # Detect layout using LayoutParser
            layout = self.model.detect(image)
            
            # Convert to standard format
            elements = []
            for element in layout:
                bbox = element.block
                elements.append({
                    'type': element.type,
                    'bbox': [bbox.x_1, bbox.y_1, bbox.x_2, bbox.y_2],
                    'confidence': element.score,
                    'area': (bbox.x_2 - bbox.x_1) * (bbox.y_2 - bbox.y_1)
                })
            
            logger.info(f"LayoutParser detected {len(elements)} layout elements")
            return elements
            
        except Exception as e:
            logger.error(f"LayoutParser detection failed: {e}")
            return self._fallback_layout_detection(image)
    
    def _fallback_layout_detection(self, image: Image.Image) -> List[Dict[str, Any]]:
        """Fallback layout detection using basic image analysis"""
        
        # Convert to numpy array
        img_array = np.array(image)
        height, width = img_array.shape[:2]
        
        # Basic layout detection based on image regions
        elements = []
        
        # Assume top 20% is title area
        if height > 100:
            elements.append({
                'type': 'Title',
                'bbox': [0, 0, width, int(height * 0.2)],
                'confidence': 0.7,
                'area': width * int(height * 0.2)
            })
        
        # Assume middle area is text
        if height > 200:
            elements.append({
                'type': 'Text',
                'bbox': [0, int(height * 0.2), width, int(height * 0.8)],
                'confidence': 0.6,
                'area': width * int(height * 0.6)
            })
        
        # Assume bottom area might be footer/additional content
        if height > 300:
            elements.append({
                'type': 'Text',
                'bbox': [0, int(height * 0.8), width, height],
                'confidence': 0.5,
                'area': width * int(height * 0.2)
            })
        
        logger.info(f"Fallback detection created {len(elements)} layout elements")
        return elements

class EnhancedLayoutProcessor:
    """Enhanced layout processor that combines multiple approaches"""
    
    def __init__(self):
        self.layoutparser_processor = LayoutParserProcessor()
        
    def process_image(self, image_path: Path) -> Dict[str, Any]:
        """
        Process an image to extract layout information
        
        Args:
            image_path: Path to the image file
            
        Returns:
            Dictionary with layout analysis results
        """
        try:
            # Load image
            image = Image.open(image_path)
            
            # Detect layout elements
            elements = self.layoutparser_processor.detect_layout(image)
            
            # Analyze layout structure
            analysis = self._analyze_layout_structure(elements, image.size)
            
            return {
                'success': True,
                'image_size': image.size,
                'elements': elements,
                'analysis': analysis,
                'layoutparser_used': self.layoutparser_processor.available
            }
            
        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'elements': [],
                'analysis': {},
                'layoutparser_used': False
            }
    
    def _analyze_layout_structure(self, elements: List[Dict[str, Any]], image_size: Tuple[int, int]) -> Dict[str, Any]:
        """Analyze the overall layout structure"""
        
        if not elements:
            return {'structure_type': 'unknown', 'reading_order': []}
        
        # Count element types
        type_counts = {}
        for element in elements:
            element_type = element['type']
            type_counts[element_type] = type_counts.get(element_type, 0) + 1
        
        # Determine document structure
        structure_type = 'document'
        if type_counts.get('Table', 0) > 0:
            structure_type = 'table_document'
        elif type_counts.get('Figure', 0) > 0:
            structure_type = 'figure_document'
        elif type_counts.get('Title', 0) > 0 and type_counts.get('Text', 0) > 0:
            structure_type = 'article'
        
        # Determine reading order (top to bottom, left to right)
        sorted_elements = sorted(elements, key=lambda x: (x['bbox'][1], x['bbox'][0]))
        reading_order = [i for i, _ in enumerate(sorted_elements)]
        
        return {
            'structure_type': structure_type,
            'type_counts': type_counts,
            'reading_order': reading_order,
            'total_elements': len(elements)
        }

# Example usage and testing
def test_enhanced_layout_processor():
    """Test the enhanced layout processor"""
    
    processor = EnhancedLayoutProcessor()
    
    # Create a test image
    test_image = Image.new('RGB', (800, 600), 'white')
    test_path = Path('test_layout_image.png')
    test_image.save(test_path)
    
    try:
        # Process the test image
        result = processor.process_image(test_path)
        
        print("🧪 Layout Processing Test Results:")
        print(f"   Success: {result['success']}")
        print(f"   LayoutParser used: {result['layoutparser_used']}")
        print(f"   Elements detected: {len(result['elements'])}")
        print(f"   Structure type: {result['analysis'].get('structure_type', 'unknown')}")
        
        return result['success']
        
    finally:
        # Clean up
        if test_path.exists():
            test_path.unlink()

if __name__ == "__main__":
    test_enhanced_layout_processor()
