
"""
Integration Example: Adding LayoutParser to Enhanced Document Processing

This example shows how to integrate LayoutParser into the existing
enhanced document processing pipeline.
"""

import asyncio
import os
from pathlib import Path
from typing import Dict, Any, List

# Import the enhanced layout processor
from enhanced_layout_processor import EnhancedLayoutProcessor

async def process_document_with_layoutparser(pdf_path: Path) -> Dict[str, Any]:
    """
    Process a PDF document using enhanced processing with LayoutParser
    
    Args:
        pdf_path: Path to the PDF file
        
    Returns:
        Processing results with layout analysis
    """
    
    try:
        # Import the existing enhanced processor
        from docqa.advanced_ingestion.layout_aware_processor import LayoutAwareProcessor, AdvancedConfig
        
        # Create enhanced configuration with LayoutParser enabled
        config = AdvancedConfig(
            # Layout Analysis - Enable LayoutParser
            use_layoutparser=True,
            
            # OCR Engines
            use_tesseract=True,
            use_easyocr=True,
            use_paddleocr=False,
            ocr_languages=['en'],
            
            # AI Analysis
            use_gpt4_vision=True,
            use_gpt4_text_enhancement=True,
            analyze_document_structure=True,
            extract_key_entities=True,
            
            # Text Processing
            enhance_text_quality=True,
            preserve_reading_order=True,
            merge_text_blocks=True,
            
            # Chunking Strategy
            use_layout_aware_chunking=True,
            respect_section_boundaries=True,
            chunk_size=1000,
            chunk_overlap=200,
            
            # Performance
            parallel_processing=False,
            max_workers=2,
            timeout_seconds=300,
            
            # Feature toggles
            extract_tables=True,
            extract_images=True,
            analyze_charts=True
        )
        
        # Initialize processor with LayoutParser support
        processor = LayoutAwareProcessor(
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            config=config
        )
        
        # Add LayoutParser integration
        layout_processor = EnhancedLayoutProcessor()
        
        print(f"📄 Processing document with LayoutParser: {pdf_path}")
        
        # Process document
        result = await processor.process_document(pdf_path)
        
        # Add LayoutParser analysis for each page image
        if hasattr(result, 'images') and result.images:
            layout_analyses = []
            
            for i, image_info in enumerate(result.images):
                if 'image_path' in image_info:
                    layout_result = layout_processor.process_image(Path(image_info['image_path']))
                    layout_analyses.append({
                        'page': i + 1,
                        'layout_analysis': layout_result
                    })
            
            # Add layout analysis to result
            result.layout_analyses = layout_analyses
        
        print(f"✅ Document processing completed with LayoutParser integration")
        print(f"   Text extracted: {len(result.structured_text):,} characters")
        print(f"   Layout elements: {len(result.layout_elements)}")
        print(f"   Semantic chunks: {len(result.semantic_chunks)}")
        
        return {
            'success': True,
            'result': result,
            'layoutparser_used': layout_processor.layoutparser_processor.available
        }
        
    except Exception as e:
        print(f"❌ Document processing failed: {e}")
        return {
            'success': False,
            'error': str(e),
            'layoutparser_used': False
        }

async def test_integration():
    """Test the LayoutParser integration"""
    
    # Test with the Coochie Information Pack
    pdf_path = Path("Coochie_Information pack.pdf")
    
    if not pdf_path.exists():
        print(f"❌ Test PDF not found: {pdf_path}")
        return False
    
    print("🧪 Testing LayoutParser Integration")
    print("=" * 50)
    
    result = await process_document_with_layoutparser(pdf_path)
    
    if result['success']:
        print("🎉 LayoutParser integration test successful!")
        print(f"   LayoutParser used: {result['layoutparser_used']}")
        
        # Show some results
        doc_result = result['result']
        print(f"   Text length: {len(doc_result.structured_text):,} characters")
        print(f"   Layout elements: {len(doc_result.layout_elements)}")
        print(f"   Chunks created: {len(doc_result.semantic_chunks)}")
        
        if hasattr(doc_result, 'layout_analyses'):
            print(f"   Layout analyses: {len(doc_result.layout_analyses)} pages")
        
        return True
    else:
        print(f"❌ Integration test failed: {result.get('error', 'Unknown error')}")
        return False

if __name__ == "__main__":
    asyncio.run(test_integration())
