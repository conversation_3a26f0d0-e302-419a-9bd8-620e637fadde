#!/usr/bin/env python3
"""
Terminal QnA Interface

Interactive terminal interface for asking questions about the Coochie Information Pack
using the enhanced document processing system.
"""

import asyncio
import os
import sys
import time
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging to be less verbose for terminal use
logging.basicConfig(level=logging.WARNING)

# Configuration
PDF_PATH = "Coochie_Information pack.pdf"

class TerminalQnA:
    """Terminal-based QnA interface"""
    
    def __init__(self):
        self.processor = None
        self.document_result = None
        self.is_ready = False
    
    async def initialize(self):
        """Initialize the enhanced document processor"""
        print("🚀 Initializing Enhanced QnA System...")
        print("=" * 50)
        
        try:
            from docqa.advanced_ingestion.layout_aware_processor import LayoutAwareProcessor, AdvancedConfig
            
            # Create optimized configuration for terminal use
            config = AdvancedConfig(
                use_layoutparser=True,
                use_tesseract=True,
                use_easyocr=True,
                use_gpt4_vision=True,
                use_gpt4_text_enhancement=True,
                analyze_document_structure=True,
                extract_key_entities=True,
                enhance_text_quality=True,
                preserve_reading_order=True,
                merge_text_blocks=True,
                use_layout_aware_chunking=True,
                respect_section_boundaries=True,
                chunk_size=1000,
                chunk_overlap=200,
                parallel_processing=False,
                max_workers=2,
                timeout_seconds=300,
                extract_tables=False,
                extract_images=True,
                analyze_charts=True
            )
            
            # Initialize processor
            self.processor = LayoutAwareProcessor(
                openai_api_key=os.getenv('OPENAI_API_KEY', ''),
                config=config
            )
            
            if not os.path.exists(PDF_PATH):
                print(f"❌ PDF file not found: {PDF_PATH}")
                return False
            
            print(f"📄 Processing document: {PDF_PATH}")
            print("⏳ This may take 30-60 seconds...")
            
            start_time = time.time()
            
            # Process document
            self.document_result = await self.processor.process_document(Path(PDF_PATH))
            
            processing_time = time.time() - start_time
            
            print(f"✅ Document processed in {processing_time:.1f}s")
            print(f"   📝 Text extracted: {len(self.document_result.structured_text):,} characters")
            print(f"   🏗️  Layout elements: {len(self.document_result.layout_elements)}")
            print(f"   🧩 Semantic chunks: {len(self.document_result.semantic_chunks)}")
            
            self.is_ready = True
            return True
            
        except Exception as e:
            print(f"❌ Initialization failed: {e}")
            return False
    
    def answer_question(self, question: str) -> str:
        """Answer a question using the enhanced content"""
        if not self.is_ready:
            return "❌ System not ready. Please wait for initialization to complete."
        
        try:
            # Find relevant chunks
            relevant_chunks = self.find_relevant_chunks(question)
            
            # Generate answer
            if relevant_chunks:
                context = "\n\n".join([chunk['content'] for chunk in relevant_chunks[:3]])
                answer = self.extract_answer_from_context(question, context)
            else:
                # Fallback to full text search
                context = self.find_relevant_text_sections(question)
                answer = self.extract_answer_from_context(question, context)
            
            return answer
            
        except Exception as e:
            return f"❌ Error generating answer: {e}"
    
    def find_relevant_chunks(self, question: str) -> list:
        """Find semantic chunks most relevant to the question"""
        question_lower = question.lower()
        question_keywords = self.extract_keywords(question_lower)
        
        scored_chunks = []
        
        for chunk in self.document_result.semantic_chunks:
            content = chunk.get('content', '').lower()
            score = 0
            
            # Score based on keyword matches
            for keyword in question_keywords:
                if keyword in content:
                    score += content.count(keyword) * 2
            
            # Bonus for exact phrase matches
            if any(phrase in content for phrase in [question_lower[:20], question_lower[-20:]]):
                score += 5
            
            if score > 0:
                scored_chunks.append((score, chunk))
        
        # Return top chunks sorted by relevance
        scored_chunks.sort(key=lambda x: x[0], reverse=True)
        return [chunk for score, chunk in scored_chunks[:5]]
    
    def find_relevant_text_sections(self, question: str) -> str:
        """Find relevant sections in the full text"""
        question_lower = question.lower()
        keywords = self.extract_keywords(question_lower)
        
        sentences = self.document_result.structured_text.split('.')
        relevant_sentences = []
        
        for sentence in sentences:
            sentence_lower = sentence.lower()
            score = sum(2 for keyword in keywords if keyword in sentence_lower)
            
            if score > 0:
                relevant_sentences.append((score, sentence.strip()))
        
        # Sort by relevance and return top sentences
        relevant_sentences.sort(key=lambda x: x[0], reverse=True)
        return '. '.join([sentence for score, sentence in relevant_sentences[:5]])
    
    def extract_keywords(self, text: str) -> list:
        """Extract keywords from question"""
        stop_words = {'what', 'is', 'the', 'how', 'does', 'are', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an'}
        words = text.split()
        keywords = [word.strip('?.,!') for word in words if word.lower() not in stop_words and len(word) > 2]
        return keywords
    
    def extract_answer_from_context(self, question: str, context: str) -> str:
        """Extract answer from context"""
        if not context or len(context.strip()) < 10:
            return "❌ I couldn't find specific information about this question in the document."
        
        question_lower = question.lower()
        
        # Financial questions
        if any(word in question_lower for word in ['fee', 'cost', 'investment', 'price', 'money']):
            import re
            money_pattern = r'\$[\d,]+(?:\.\d{2})?'
            money_matches = re.findall(money_pattern, context)
            if money_matches:
                return f"💰 Financial Information:\n{', '.join(set(money_matches[:5]))}\n\n📄 Details: {context[:400]}..."
        
        # Royalty questions
        if 'royalty' in question_lower:
            if '10%' in context or '10.0%' in context:
                return f"💰 Royalty Information:\nThe royalty fee is 10% of monthly revenue.\n\n📄 Details: {context[:400]}..."
        
        # Training/Support questions
        if any(word in question_lower for word in ['training', 'support', 'help', 'assistance']):
            return f"🤝 Training & Support:\n{context[:500]}..."
        
        # Business model questions
        if any(word in question_lower for word in ['business model', 'hydrogreen', 'how does', 'work']):
            return f"🏢 Business Model:\n{context[:500]}..."
        
        # Territory questions
        if any(word in question_lower for word in ['territory', 'area', 'coverage', 'location']):
            return f"📍 Territory Information:\n{context[:400]}..."
        
        # Benefits questions
        if any(word in question_lower for word in ['benefit', 'advantage', 'why choose']):
            return f"✨ Benefits:\n{context[:500]}..."
        
        # Default answer
        return f"📄 Answer:\n{context[:600]}..."
    
    def show_help(self):
        """Show help information"""
        help_text = """
🤖 COOCHIE QnA SYSTEM - HELP
=" * 40

Available Commands:
  help, h          - Show this help message
  quit, exit, q    - Exit the QnA system
  stats            - Show document processing statistics
  examples         - Show example questions

Question Categories:
  💰 Financial     - fees, costs, investment, royalties
  🤝 Support       - training, support, assistance
  🏢 Business      - business model, how it works
  📍 Territory     - area coverage, location
  ✨ Benefits      - advantages, why choose
  🔧 Service       - lawn care program, treatments

Tips:
  • Ask specific questions for better answers
  • Use keywords like "fee", "training", "business model"
  • Questions are processed using enhanced AI analysis
  • The system has analyzed 41,000+ characters from the document

Example Questions:
  "What is the franchise fee?"
  "What training is provided?"
  "How does the business model work?"
  "What are the ongoing costs?"
"""
        print(help_text)
    
    def show_examples(self):
        """Show example questions"""
        examples = """
💡 EXAMPLE QUESTIONS:

💰 Financial Questions:
  • What is the franchise fee for Coochie?
  • What are the ongoing royalty fees?
  • What initial investment is required?
  • What are the monthly costs?

🤝 Support Questions:
  • What training is provided to franchisees?
  • What ongoing support is available?
  • Is there a support hotline?

🏢 Business Model Questions:
  • What is Coochie HydroGreen?
  • How does the lawn care program work?
  • What services do franchisees provide?

📍 Territory Questions:
  • What territory coverage is provided?
  • How are areas allocated?

✨ Benefits Questions:
  • What are the benefits of becoming a franchisee?
  • Why choose Coochie over other franchises?
"""
        print(examples)
    
    def show_stats(self):
        """Show document processing statistics"""
        if not self.is_ready:
            print("❌ System not ready yet.")
            return
        
        stats = f"""
📊 DOCUMENT PROCESSING STATISTICS:

📄 Document: {PDF_PATH}
📝 Text Content:
   • Raw text: {len(self.document_result.raw_text):,} characters
   • Enhanced text: {len(self.document_result.structured_text):,} characters
   • Improvement: {len(self.document_result.structured_text) / max(len(self.document_result.raw_text), 1):.1f}x

🏗️  Layout Analysis:
   • Layout elements: {len(self.document_result.layout_elements)}
   • Images: {len(self.document_result.images)}
   • Charts: {len(self.document_result.charts)}

🧩 Semantic Processing:
   • Semantic chunks: {len(self.document_result.semantic_chunks)}
   • Average chunk size: {sum(len(chunk['content']) for chunk in self.document_result.semantic_chunks) // len(self.document_result.semantic_chunks) if self.document_result.semantic_chunks else 0} characters

🎯 System Status: READY FOR QUESTIONS
"""
        print(stats)
    
    async def run_interactive_session(self):
        """Run the interactive QnA session"""
        print("\n🤖 COOCHIE QnA SYSTEM - INTERACTIVE MODE")
        print("=" * 50)
        print("Type your questions about the Coochie Information Pack")
        print("Commands: 'help' for help, 'quit' to exit")
        print("=" * 50)
        
        while True:
            try:
                # Get user input
                question = input("\n❓ Your Question: ").strip()
                
                if not question:
                    continue
                
                # Handle commands
                if question.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye! Thanks for using the Coochie QnA System!")
                    break
                elif question.lower() in ['help', 'h']:
                    self.show_help()
                    continue
                elif question.lower() == 'examples':
                    self.show_examples()
                    continue
                elif question.lower() == 'stats':
                    self.show_stats()
                    continue
                
                # Process question
                print("\n🔍 Processing your question...")
                start_time = time.time()
                
                answer = self.answer_question(question)
                
                response_time = time.time() - start_time
                
                print(f"\n🤖 Answer (responded in {response_time:.1f}s):")
                print("-" * 50)
                print(answer)
                print("-" * 50)
                
            except KeyboardInterrupt:
                print("\n\n👋 Goodbye! Thanks for using the Coochie QnA System!")
                break
            except Exception as e:
                print(f"\n❌ Error: {e}")

async def main():
    """Main function"""
    print("🚀 COOCHIE QnA TERMINAL INTERFACE")
    print("=" * 50)
    
    # Check prerequisites
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY environment variable not set")
        print("💡 Please set your OpenAI API key in the .env file")
        sys.exit(1)
    
    if not os.path.exists(PDF_PATH):
        print(f"❌ PDF file not found: {PDF_PATH}")
        sys.exit(1)
    
    # Initialize QnA system
    qna = TerminalQnA()
    
    if await qna.initialize():
        print("\n✅ System ready! You can now ask questions.")
        await qna.run_interactive_session()
    else:
        print("❌ Failed to initialize QnA system")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
