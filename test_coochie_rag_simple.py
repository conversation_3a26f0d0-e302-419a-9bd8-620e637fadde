#!/usr/bin/env python3
"""
Simple RAG Test for Coochie Information Pack PDF

This script provides a focused test of the RAG functionality:
1. Upload the Coochie Information Pack PDF
2. Test document-specific questions
3. Test general RAG questions
4. Validate AI responses

Usage: python test_coochie_rag_simple.py
"""

import requests
import time
import os
from typing import Dict, Any, List

# Configuration
BASE_URL = "http://localhost:8000/api"
LOGIN_EMAIL = "<EMAIL>"
LOGIN_PASSWORD = "Admin@1234"
PDF_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"

class SimpleCoochieRAGTester:
    """Simple RAG tester for Coochie Information Pack"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.token = None
        self.headers = {}
        self.document_id = None
        
    def login(self) -> bool:
        """Login and get authentication token"""
        print("🔐 Logging in...")
        
        login_data = {
            "email_or_mobile": LOGIN_EMAIL,
            "password": LOGIN_PASSWORD,
            "remember_me": True
        }
        
        try:
            response = requests.post(f"{self.base_url}/auth/login", json=login_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.token = result["data"]["details"]["access_token"]
                    self.headers = {
                        "Authorization": f"Bearer {self.token}",
                        "Content-Type": "application/json"
                    }
                    print("✅ Login successful!")
                    return True
            
            print(f"❌ Login failed: {response.text}")
            return False
            
        except Exception as e:
            print(f"❌ Login error: {str(e)}")
            return False
    
    def upload_pdf(self) -> bool:
        """Upload the Coochie PDF"""
        print("\n📤 Uploading Coochie Information Pack PDF...")
        
        if not os.path.exists(PDF_PATH):
            print(f"❌ PDF file not found: {PDF_PATH}")
            return False
        
        try:
            with open(PDF_PATH, 'rb') as pdf_file:
                files = {
                    'file': ('Coochie_Information_pack.pdf', pdf_file, 'application/pdf')
                }
                
                data = {
                    'name': 'Coochie Hydrogreen Information Pack',
                    'description': 'Test upload of Coochie Hydrogreen franchise information',
                    'is_active': True
                }
                
                upload_headers = {"Authorization": f"Bearer {self.token}"}
                
                response = requests.post(
                    f"{self.base_url}/documents/upload",
                    files=files,
                    data=data,
                    headers=upload_headers,
                    timeout=120
                )
            
            if response.status_code == 201:
                result = response.json()
                self.document_id = result["data"]["id"]
                print(f"✅ PDF uploaded successfully!")
                print(f"   Document ID: {self.document_id}")
                print("⏳ Waiting for processing...")
                time.sleep(30)  # Wait for processing
                return True
            else:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Upload error: {str(e)}")
            return False
    
    def test_document_questions(self) -> bool:
        """Test questions about the specific document"""
        print(f"\n❓ Testing document-specific questions...")
        
        questions = [
            "What are the investment requirements for Coochie Hydrogreen?",
            "What franchise fees does Coochie Hydrogreen charge?",
            "What support does Coochie Hydrogreen provide?",
            "What are the territory requirements?",
            "Is this an eco-friendly business?"
        ]
        
        successful = 0
        
        for i, question in enumerate(questions, 1):
            print(f"\n🔍 Question {i}: {question}")
            
            try:
                question_data = {
                    "question": question,
                    "top_k": 5,
                    "similarity_threshold": 0.6
                }
                
                response = requests.post(
                    f"{self.base_url}/docqa/ask/document/{self.document_id}",
                    json=question_data,
                    headers=self.headers,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result["data"]["answer"]
                    
                    print(f"✅ Answer: {answer[:150]}...")
                    successful += 1
                else:
                    print(f"❌ Failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)}")
            
            time.sleep(2)
        
        print(f"\n📊 Document Questions: {successful}/{len(questions)} successful")
        return successful > 0
    
    def test_general_questions(self) -> bool:
        """Test general RAG questions"""
        print(f"\n❓ Testing general RAG questions...")
        
        questions = [
            "What franchise opportunities are available?",
            "Tell me about eco-friendly car wash franchises",
            "What are the costs for car wash franchises?",
            "Which franchises are environmentally friendly?"
        ]
        
        successful = 0
        
        for i, question in enumerate(questions, 1):
            print(f"\n🔍 General Question {i}: {question}")
            
            try:
                question_data = {
                    "question": question,
                    "top_k": 6,
                    "similarity_threshold": 0.7
                }
                
                response = requests.post(
                    f"{self.base_url}/docqa/ask",
                    json=question_data,
                    headers=self.headers,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    answer = result["data"]["answer"]
                    
                    print(f"✅ Answer: {answer[:150]}...")
                    
                    # Check if Coochie is mentioned
                    if "coochie" in answer.lower():
                        print("✅ Coochie Hydrogreen detected in answer")
                    
                    successful += 1
                else:
                    print(f"❌ Failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)}")
            
            time.sleep(2)
        
        print(f"\n📊 General Questions: {successful}/{len(questions)} successful")
        return successful > 0
    
    def test_agent_chat(self) -> bool:
        """Test agent system with Coochie questions"""
        print(f"\n🤖 Testing agent system...")
        
        messages = [
            "Hello! I'm interested in franchise opportunities.",
            "Tell me about Coochie Hydrogreen franchise.",
            "What are the investment requirements?",
            "I want to learn more about the support provided."
        ]
        
        session_id = f"test_session_{int(time.time())}"
        successful = 0
        
        for i, message in enumerate(messages, 1):
            print(f"\n🤖 Message {i}: {message}")
            
            try:
                chat_data = {
                    "message": message,
                    "session_id": session_id
                }
                
                response = requests.post(
                    f"{self.base_url}/agents/chat",
                    json=chat_data,
                    headers=self.headers,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    print(f"✅ Response: {result.get('response', 'No response')[:150]}...")
                    print(f"   Intent: {result.get('intent', 'Unknown')}")
                    successful += 1
                else:
                    print(f"❌ Failed: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {str(e)}")
            
            time.sleep(3)
        
        print(f"\n📊 Agent Chat: {successful}/{len(messages)} successful")
        return successful > 0
    
    def run_test(self) -> bool:
        """Run the complete test"""
        print("🧪 Simple Coochie RAG Test")
        print("="*50)
        
        # Step 1: Login
        if not self.login():
            return False
        
        # Step 2: Upload PDF
        if not self.upload_pdf():
            return False
        
        # Step 3: Test document questions
        doc_success = self.test_document_questions()
        
        # Step 4: Test general questions
        general_success = self.test_general_questions()
        
        # Step 5: Test agent system
        agent_success = self.test_agent_chat()
        
        # Summary
        print("\n" + "="*50)
        print("📊 TEST SUMMARY")
        print("="*50)
        
        results = {
            "Document Questions": doc_success,
            "General RAG Questions": general_success,
            "Agent System": agent_success
        }
        
        for test_name, result in results.items():
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
        
        overall_success = all(results.values())
        
        if overall_success:
            print("\n🎉 All tests passed! RAG system is working correctly.")
        else:
            print("\n⚠️ Some tests failed. Check the output above.")
        
        print(f"\nDocument ID: {self.document_id}")
        print("Check server logs for detailed AI responses.")
        
        return overall_success


def main():
    """Main function"""
    print("🚀 Starting Simple Coochie RAG Test")
    print("Make sure the server is running on http://localhost:8000")
    print()
    
    tester = SimpleCoochieRAGTester()
    
    try:
        success = tester.run_test()
        return success
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        return False
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
