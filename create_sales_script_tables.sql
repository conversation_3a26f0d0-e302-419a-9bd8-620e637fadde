-- Create Sales Script Tables
-- Migration for dynamic sales script system with conversation state management

-- 1. Sales Scripts Table
CREATE TABLE IF NOT EXISTS sales_scripts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    franchisor_id UUID NOT NULL REFERENCES franchisors(id),
    script_title VARCHAR(255) NOT NULL,
    script_body TEXT NOT NULL,
    script_type VARCHAR(50) DEFAULT 'greeting',
    is_active BOOLEAN DEFAULT true NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 2. Conversation States Table
CREATE TABLE IF NOT EXISTS conversation_states (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lead_id UUID NOT NULL REFERENCES leads(id),
    phone_number VARCHAR(20) NOT NULL,
    franchisor_id UUID NOT NULL REFERENCES franchisors(id),
    current_stage VARCHAR(50) DEFAULT 'greeting',
    qualification_session_id UUID REFERENCES qualification_sessions(id),
    current_question_index INTEGER DEFAULT 0,
    context_data JSONB,
    is_active BOOLEAN DEFAULT true,
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- 3. Conversation Messages Table
CREATE TABLE IF NOT EXISTS conversation_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_state_id UUID NOT NULL REFERENCES conversation_states(id),
    message_text TEXT NOT NULL,
    message_type VARCHAR(20) NOT NULL, -- inbound, outbound
    message_source VARCHAR(50) DEFAULT 'sms', -- sms, system, agent
    agent_response TEXT,
    processing_time_ms INTEGER,
    kudosity_message_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_sales_scripts_franchisor ON sales_scripts(franchisor_id);
CREATE INDEX IF NOT EXISTS idx_sales_scripts_active ON sales_scripts(is_active);
CREATE INDEX IF NOT EXISTS idx_sales_scripts_type ON sales_scripts(script_type);

CREATE INDEX IF NOT EXISTS idx_conversation_states_lead ON conversation_states(lead_id);
CREATE INDEX IF NOT EXISTS idx_conversation_states_phone ON conversation_states(phone_number);
CREATE INDEX IF NOT EXISTS idx_conversation_states_franchisor ON conversation_states(franchisor_id);
CREATE INDEX IF NOT EXISTS idx_conversation_states_stage ON conversation_states(current_stage);
CREATE INDEX IF NOT EXISTS idx_conversation_states_active ON conversation_states(is_active);

CREATE INDEX IF NOT EXISTS idx_conversation_messages_state ON conversation_messages(conversation_state_id);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_type ON conversation_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_conversation_messages_kudosity ON conversation_messages(kudosity_message_id);

-- Insert initial sales script template
INSERT INTO sales_scripts (franchisor_id, script_title, script_body, script_type, is_active)
SELECT 
    f.id as franchisor_id,
    'Initial Greeting' as script_title,
    'Hi, this is {{name_of_cust_rep}}, calling you on behalf of the "{{company_name}}". 

You inquired about this opportunity. I want to give you information about this. 

Is this a good time to chat?' as script_body,
    'greeting' as script_type,
    true as is_active
FROM franchisors f
WHERE f.company_name = 'Coochie Lawn Care'
ON CONFLICT DO NOTHING;

-- Insert additional script templates for Coochie Lawn Care
INSERT INTO sales_scripts (franchisor_id, script_title, script_body, script_type, is_active)
SELECT 
    f.id as franchisor_id,
    'Qualification Introduction' as script_title,
    'Great! I''d like to ask you a few quick questions to better understand your background and interests. This will help me provide you with the most relevant information about the {{company_name}} opportunity.

Are you ready to get started?' as script_body,
    'qualification' as script_type,
    true as is_active
FROM franchisors f
WHERE f.company_name = 'Coochie Lawn Care'
ON CONFLICT DO NOTHING;

INSERT INTO sales_scripts (franchisor_id, script_title, script_body, script_type, is_active)
SELECT 
    f.id as franchisor_id,
    'Document Q&A Introduction' as script_title,
    'I''d be happy to answer any questions you have about {{company_name}}. 

I have detailed information about our franchise model, investment requirements, training programs, and ongoing support.

What would you like to know more about?' as script_body,
    'qa' as script_type,
    true as is_active
FROM franchisors f
WHERE f.company_name = 'Coochie Lawn Care'
ON CONFLICT DO NOTHING;

INSERT INTO sales_scripts (franchisor_id, script_title, script_body, script_type, is_active)
SELECT 
    f.id as franchisor_id,
    'Appointment Scheduling' as script_title,
    'Based on our conversation, you seem like a great fit for the {{company_name}} opportunity! 

I''d like to schedule a more detailed discussion with our franchise development team. This will give you a chance to learn more about the investment, training, and support we provide.

Would you prefer a call this week or next week?' as script_body,
    'scheduling' as script_type,
    true as is_active
FROM franchisors f
WHERE f.company_name = 'Coochie Lawn Care'
ON CONFLICT DO NOTHING;

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sales_scripts_updated_at 
    BEFORE UPDATE ON sales_scripts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversation_states_updated_at 
    BEFORE UPDATE ON conversation_states 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE sales_scripts IS 'Dynamic sales script templates with Jinja2 placeholders for personalized messaging';
COMMENT ON TABLE conversation_states IS 'SMS conversation state tracking for lead qualification workflow';
COMMENT ON TABLE conversation_messages IS 'Message history for SMS conversations with AI agent responses';

COMMENT ON COLUMN sales_scripts.script_body IS 'Jinja2 template with placeholders like {{company_name}}, {{lead_first_name}}';
COMMENT ON COLUMN conversation_states.current_stage IS 'Current conversation stage: greeting, qualification, qa, scheduling, completed';
COMMENT ON COLUMN conversation_states.context_data IS 'JSON context for script rendering (rep_name, lead_info, etc.)';
COMMENT ON COLUMN conversation_messages.message_type IS 'Message direction: inbound (from lead), outbound (to lead)';
COMMENT ON COLUMN conversation_messages.agent_response IS 'AI agent generated response for the message';
