"""
Layout-Aware Document Processor with Multi-Modal Intelligence

This advanced system combines the most powerful document processing tools:
- LayoutParser for document layout analysis and structure understanding
- Tesseract OCR with advanced preprocessing
- OpenAI GPT-4 Vision for image/chart analysis
- EasyOCR for multilingual text recognition
- PaddleOCR for additional OCR capabilities
- Semantic chunking for better context preservation
- Multi-modal content understanding and cross-referencing
"""

import fitz  # PyMuPDF
import pytesseract
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
import cv2
import numpy as np
import pandas as pd
import camelot
import tabula
import pdfplumber
# import layoutparser as lp  # Optional - will fallback to basic detection if not available
try:
    import layoutparser as lp
    LAYOUTPARSER_AVAILABLE = True
except ImportError:
    LAYOUTPARSER_AVAILABLE = False
    lp = None
import easyocr
from typing import List, Dict, Any, Optional, Tuple, Union
import io
import base64
import re
from pathlib import Path
import logging
from dataclasses import dataclass, field
import openai
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
import json
import asyncio
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document
import torch
import torchvision.transforms as transforms
from transformers import pipeline, AutoTokenizer, AutoModel
import spacy

logger = logging.getLogger(__name__)

@dataclass
class LayoutElement:
    """Represents a layout element detected by LayoutParser"""
    type: str  # text, title, list, table, figure
    bbox: Tuple[float, float, float, float]  # x1, y1, x2, y2
    confidence: float
    content: str
    page_num: int
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ProcessedDocument:
    """Complete processed document with layout-aware content"""
    raw_text: str
    structured_text: str
    layout_elements: List[LayoutElement]
    images: List[Dict[str, Any]]
    tables: List[Dict[str, Any]]
    charts: List[Dict[str, Any]]
    semantic_chunks: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    processing_stats: Dict[str, Any]

@dataclass
class AdvancedConfig:
    """Configuration for advanced document processing"""
    # Layout Analysis
    use_layoutparser: bool = True
    layout_model: str = "lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config"  # or detectron2://
    layout_confidence_threshold: float = 0.5
    
    # OCR Engines
    use_tesseract: bool = True
    use_easyocr: bool = True
    use_paddleocr: bool = False
    ocr_languages: List[str] = field(default_factory=lambda: ['en'])
    tesseract_config: str = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?@#$%^&*()_+-=[]{}|;:,.<>?/~` '
    
    # AI Analysis
    use_gpt4_vision: bool = True
    use_gpt4_text_enhancement: bool = True
    analyze_document_structure: bool = True
    extract_key_entities: bool = True
    cross_reference_content: bool = True
    
    # Text Processing
    enhance_text_quality: bool = True
    preserve_reading_order: bool = True
    merge_text_blocks: bool = True
    detect_languages: bool = True
    
    # Chunking Strategy
    use_layout_aware_chunking: bool = True
    respect_section_boundaries: bool = True
    chunk_size: int = 1000
    chunk_overlap: int = 200
    
    # Performance
    parallel_processing: bool = True
    max_workers: int = 6
    gpu_acceleration: bool = True
    timeout_seconds: int = 600

class LayoutAwareProcessor:
    """Advanced document processor with layout understanding"""
    
    def __init__(self, openai_api_key: str, config: AdvancedConfig = None):
        self.openai_client = openai.OpenAI(api_key=openai_api_key)
        self.config = config or AdvancedConfig()
        
        # Initialize LayoutParser model
        self.layout_model = None
        if self.config.use_layoutparser and LAYOUTPARSER_AVAILABLE:
            try:
                self.layout_model = lp.Detectron2LayoutModel(
                    self.config.layout_model,
                    extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", self.config.layout_confidence_threshold],
                    label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
                )
                logger.info("LayoutParser model loaded successfully")
            except Exception as e:
                logger.warning(f"LayoutParser initialization failed: {e}")
                self.layout_model = None
        elif self.config.use_layoutparser and not LAYOUTPARSER_AVAILABLE:
            logger.warning("LayoutParser requested but not available. Install with: pip install layoutparser[layoutmodels]")
            self.config.use_layoutparser = False
        
        # Initialize OCR engines
        self.tesseract_available = self._check_tesseract()
        self.easyocr_reader = None
        self.paddleocr_reader = None
        
        if self.config.use_easyocr:
            try:
                self.easyocr_reader = easyocr.Reader(
                    self.config.ocr_languages,
                    gpu=self.config.gpu_acceleration and torch.cuda.is_available()
                )
                logger.info("EasyOCR initialized successfully")
            except Exception as e:
                logger.warning(f"EasyOCR initialization failed: {e}")
        
        if self.config.use_paddleocr:
            try:
                from paddleocr import PaddleOCR
                self.paddleocr_reader = PaddleOCR(
                    use_angle_cls=True,
                    lang='en',
                    use_gpu=self.config.gpu_acceleration and torch.cuda.is_available()
                )
                logger.info("PaddleOCR initialized successfully")
            except Exception as e:
                logger.warning(f"PaddleOCR initialization failed: {e}")
        
        # Initialize NLP tools
        self.nlp = None
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            logger.warning("spaCy model not found. Install with: python -m spacy download en_core_web_sm")
        
        # Initialize semantic text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            separators=["\n\n", "\n", ". ", " ", ""],
            keep_separator=True
        )
        
        # Text enhancement patterns
        self.text_patterns = {
            'franchise_terms': re.compile(r'\b(?:franchise|franchisor|franchisee|royalty|territory|investment|fee)\b', re.IGNORECASE),
            'financial_terms': re.compile(r'\$[\d,]+(?:\.\d{2})?|\b\d+\s*(?:dollars?|USD|AUD|CAD|EUR|percent|%)\b', re.IGNORECASE),
            'contact_info': re.compile(r'\b(?:\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b|[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}', re.IGNORECASE),
            'business_metrics': re.compile(r'\b(?:ROI|profit|revenue|margin|growth|market\s+share|EBITDA)\b', re.IGNORECASE),
            'locations': re.compile(r'\b(?:location|territory|area|region|state|city|address)\b', re.IGNORECASE),
            'requirements': re.compile(r'\b(?:requirement|qualification|criteria|minimum|maximum|must|should)\b', re.IGNORECASE),
            'support_terms': re.compile(r'\b(?:training|support|assistance|help|guidance|mentoring)\b', re.IGNORECASE),
            'dates': re.compile(r'\b(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4}|\d{4}[/-]\d{1,2}[/-]\d{1,2}|\w+ \d{1,2},? \d{4})\b'),
            'headings': re.compile(r'^[A-Z][A-Z\s]{2,}$', re.MULTILINE),
            'bullet_points': re.compile(r'^[\s]*[•·▪▫◦‣⁃]\s*', re.MULTILINE),
            'numbered_lists': re.compile(r'^[\s]*\d+[\.\)]\s*', re.MULTILINE)
        }
    
    def _check_tesseract(self) -> bool:
        """Check if Tesseract is available"""
        try:
            pytesseract.get_tesseract_version()
            return True
        except Exception:
            logger.warning("Tesseract not found. Install with: apt-get install tesseract-ocr")
            return False
    
    async def process_document(self, file_path: Path) -> ProcessedDocument:
        """
        Process document with layout-aware intelligence
        
        Args:
            file_path: Path to the PDF document
            
        Returns:
            ProcessedDocument with comprehensive layout-aware content
        """
        logger.info(f"Starting layout-aware document processing: {file_path}")
        start_time = time.time()
        
        processing_stats = {
            'start_time': start_time,
            'pages_processed': 0,
            'layout_elements_detected': 0,
            'ocr_engines_used': [],
            'ai_analysis_performed': False,
            'errors': []
        }
        
        try:
            # Step 1: Analyze document layout
            layout_elements = await self._analyze_document_layout(file_path)
            processing_stats['layout_elements_detected'] = len(layout_elements)
            
            # Step 2: Extract content based on layout
            content_extraction = await self._extract_layout_aware_content(file_path, layout_elements)
            
            # Step 3: Perform multi-engine OCR on text regions
            ocr_enhanced_content = await self._enhance_with_multi_ocr(file_path, layout_elements)
            
            # Step 4: Extract and analyze structured elements
            structured_elements = await self._extract_structured_elements(file_path, layout_elements)
            
            # Step 5: AI-powered content enhancement and analysis
            ai_enhanced_content = await self._ai_enhance_content(
                content_extraction, structured_elements, layout_elements
            )
            processing_stats['ai_analysis_performed'] = True
            
            # Step 6: Create layout-aware semantic chunks
            semantic_chunks = self._create_layout_aware_chunks(
                ai_enhanced_content, layout_elements, structured_elements
            )
            
            # Step 7: Extract comprehensive metadata
            metadata = self._extract_comprehensive_metadata(
                file_path, ai_enhanced_content, layout_elements, structured_elements
            )
            
            # Finalize processing stats
            processing_stats.update({
                'end_time': time.time(),
                'total_time': time.time() - start_time,
                'pages_processed': len(set(elem.page_num for elem in layout_elements)),
                'chunks_created': len(semantic_chunks),
                'success': True
            })
            
            logger.info(f"Layout-aware processing completed in {processing_stats['total_time']:.2f}s")
            logger.info(f"Detected {len(layout_elements)} layout elements, created {len(semantic_chunks)} chunks")
            
            return ProcessedDocument(
                raw_text=content_extraction.get('raw_text', ''),
                structured_text=ai_enhanced_content.get('enhanced_text', ''),
                layout_elements=layout_elements,
                images=structured_elements.get('images', []),
                tables=structured_elements.get('tables', []),
                charts=structured_elements.get('charts', []),
                semantic_chunks=semantic_chunks,
                metadata=metadata,
                processing_stats=processing_stats
            )
            
        except Exception as e:
            processing_stats['errors'].append(str(e))
            processing_stats['success'] = False
            logger.error(f"Layout-aware document processing failed: {str(e)}")
            raise
    
    async def _analyze_document_layout(self, file_path: Path) -> List[LayoutElement]:
        """Analyze document layout using LayoutParser"""
        if not self.layout_model:
            logger.warning("LayoutParser not available, falling back to basic layout detection")
            return await self._basic_layout_detection(file_path)
        
        layout_elements = []
        
        try:
            doc = fitz.open(str(file_path))
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Convert page to image for layout analysis
                mat = fitz.Matrix(2, 2)  # 2x zoom for better detection
                pix = page.get_pixmap(matrix=mat)
                img_array = np.frombuffer(pix.tobytes(), dtype=np.uint8)
                img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                
                # Detect layout elements
                layout = self.layout_model.detect(img_rgb)
                
                # Process detected elements
                for element in layout:
                    bbox = element.block
                    element_type = element.type
                    confidence = element.score
                    
                    # Extract text from the region
                    rect = fitz.Rect(bbox.x_1/2, bbox.y_1/2, bbox.x_2/2, bbox.y_2/2)  # Scale back
                    region_text = page.get_textbox(rect)
                    
                    layout_element = LayoutElement(
                        type=element_type,
                        bbox=(bbox.x_1, bbox.y_1, bbox.x_2, bbox.y_2),
                        confidence=confidence,
                        content=region_text.strip(),
                        page_num=page_num,
                        metadata={
                            'area': (bbox.x_2 - bbox.x_1) * (bbox.y_2 - bbox.y_1),
                            'aspect_ratio': (bbox.x_2 - bbox.x_1) / (bbox.y_2 - bbox.y_1),
                            'position': 'top' if bbox.y_1 < pix.height/3 else 'middle' if bbox.y_1 < 2*pix.height/3 else 'bottom'
                        }
                    )
                    
                    layout_elements.append(layout_element)
            
            doc.close()
            
            # Sort elements by reading order (top to bottom, left to right)
            layout_elements.sort(key=lambda x: (x.page_num, x.bbox[1], x.bbox[0]))
            
            logger.info(f"Detected {len(layout_elements)} layout elements using LayoutParser")
            return layout_elements
            
        except Exception as e:
            logger.error(f"LayoutParser analysis failed: {e}")
            return await self._basic_layout_detection(file_path)
    
    async def _basic_layout_detection(self, file_path: Path) -> List[LayoutElement]:
        """Basic layout detection fallback when LayoutParser is not available"""
        layout_elements = []
        
        try:
            doc = fitz.open(str(file_path))
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                blocks = page.get_text("dict")
                
                for block in blocks.get("blocks", []):
                    if "lines" in block:  # Text block
                        bbox = block["bbox"]
                        
                        # Extract text from block
                        block_text = []
                        for line in block["lines"]:
                            for span in line.get("spans", []):
                                text = span.get("text", "").strip()
                                if text:
                                    block_text.append(text)
                        
                        content = " ".join(block_text)
                        if content.strip():
                            # Classify block type based on content and formatting
                            element_type = self._classify_text_block(content, block)
                            
                            layout_element = LayoutElement(
                                type=element_type,
                                bbox=bbox,
                                confidence=0.8,  # Default confidence for basic detection
                                content=content,
                                page_num=page_num,
                                metadata={
                                    'area': (bbox[2] - bbox[0]) * (bbox[3] - bbox[1]),
                                    'font_info': self._extract_font_info(block)
                                }
                            )
                            
                            layout_elements.append(layout_element)
            
            doc.close()
            
            logger.info(f"Detected {len(layout_elements)} layout elements using basic detection")
            return layout_elements
            
        except Exception as e:
            logger.error(f"Basic layout detection failed: {e}")
            return []
    
    def _classify_text_block(self, content: str, block: Dict) -> str:
        """Classify text block type based on content and formatting"""
        # Check for title characteristics
        if (len(content) < 100 and 
            (content.isupper() or 
             any(span.get("flags", 0) & 2**4 for line in block.get("lines", []) for span in line.get("spans", [])))):
            return "Title"
        
        # Check for list characteristics
        if (content.startswith(('•', '-', '*')) or 
            re.match(r'^\d+\.', content.strip())):
            return "List"
        
        # Default to text
        return "Text"
    
    def _extract_font_info(self, block: Dict) -> Dict[str, Any]:
        """Extract font information from text block"""
        font_info = {
            'sizes': [],
            'fonts': [],
            'flags': []
        }
        
        for line in block.get("lines", []):
            for span in line.get("spans", []):
                font_info['sizes'].append(span.get("size", 12))
                font_info['fonts'].append(span.get("font", ""))
                font_info['flags'].append(span.get("flags", 0))
        
        return {
            'avg_size': np.mean(font_info['sizes']) if font_info['sizes'] else 12,
            'dominant_font': max(set(font_info['fonts']), key=font_info['fonts'].count) if font_info['fonts'] else "",
            'has_bold': any(flag & 2**4 for flag in font_info['flags']),
            'has_italic': any(flag & 2**1 for flag in font_info['flags'])
        }

    async def _extract_layout_aware_content(self, file_path: Path, layout_elements: List[LayoutElement]) -> Dict[str, Any]:
        """Extract content based on detected layout elements"""
        content_extraction = {
            'raw_text': '',
            'structured_content': {},
            'reading_order_text': '',
            'element_texts': {}
        }

        try:
            # Group elements by page and type
            pages_content = {}
            for element in layout_elements:
                page_num = element.page_num
                if page_num not in pages_content:
                    pages_content[page_num] = {'Title': [], 'Text': [], 'List': [], 'Table': [], 'Figure': []}

                pages_content[page_num][element.type].append(element)

            # Build structured text respecting reading order
            structured_parts = []
            raw_parts = []

            for page_num in sorted(pages_content.keys()):
                page_elements = pages_content[page_num]

                structured_parts.append(f"\n=== PAGE {page_num + 1} ===\n")
                raw_parts.append(f"\n--- Page {page_num + 1} ---\n")

                # Process in logical order: Title -> Text -> List -> Table -> Figure
                for element_type in ['Title', 'Text', 'List', 'Table', 'Figure']:
                    elements = page_elements[element_type]

                    if elements:
                        if element_type == 'Title':
                            for elem in elements:
                                structured_parts.append(f"\n## {elem.content}\n")
                                raw_parts.append(elem.content)

                        elif element_type == 'Text':
                            for elem in elements:
                                structured_parts.append(f"{elem.content}\n")
                                raw_parts.append(elem.content)

                        elif element_type == 'List':
                            structured_parts.append("\n### Lists:\n")
                            for elem in elements:
                                structured_parts.append(f"- {elem.content}\n")
                                raw_parts.append(elem.content)

                        elif element_type == 'Table':
                            structured_parts.append("\n### Tables:\n")
                            for elem in elements:
                                structured_parts.append(f"[TABLE: {elem.content}]\n")
                                raw_parts.append(f"TABLE: {elem.content}")

                        elif element_type == 'Figure':
                            structured_parts.append("\n### Figures:\n")
                            for elem in elements:
                                structured_parts.append(f"[FIGURE: {elem.content}]\n")
                                raw_parts.append(f"FIGURE: {elem.content}")

            content_extraction['structured_content'] = pages_content
            content_extraction['reading_order_text'] = ''.join(structured_parts)
            content_extraction['raw_text'] = '\n\n'.join(raw_parts)

            # Store individual element texts for reference
            for element in layout_elements:
                element_id = f"page_{element.page_num}_{element.type}_{hash(element.content) % 10000}"
                content_extraction['element_texts'][element_id] = element.content

            logger.info(f"Extracted layout-aware content: {len(content_extraction['raw_text'])} characters")
            return content_extraction

        except Exception as e:
            logger.error(f"Layout-aware content extraction failed: {e}")
            return content_extraction

    async def _enhance_with_multi_ocr(self, file_path: Path, layout_elements: List[LayoutElement]) -> Dict[str, Any]:
        """Enhance content using multiple OCR engines on specific regions"""
        ocr_results = {
            'tesseract_text': '',
            'easyocr_text': '',
            'paddleocr_text': '',
            'enhanced_elements': [],
            'ocr_confidence_scores': {}
        }

        try:
            doc = fitz.open(str(file_path))

            # Process text regions that might benefit from OCR
            text_elements = [elem for elem in layout_elements if elem.type in ['Text', 'Title']]

            if self.config.parallel_processing and len(text_elements) > 3:
                # Parallel OCR processing
                with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                    futures = []

                    for element in text_elements:
                        if len(element.content.strip()) < 50:  # Short text might need OCR enhancement
                            future = executor.submit(self._ocr_element_region, doc, element)
                            futures.append((future, element))

                    for future, element in futures:
                        try:
                            ocr_result = future.result(timeout=30)
                            if ocr_result and len(ocr_result['best_text']) > len(element.content):
                                element.content = ocr_result['best_text']
                                element.metadata['ocr_enhanced'] = True
                                element.metadata['ocr_confidence'] = ocr_result['confidence']
                                ocr_results['enhanced_elements'].append(element)
                        except Exception as e:
                            logger.warning(f"OCR enhancement failed for element: {e}")
            else:
                # Sequential OCR processing
                for element in text_elements:
                    if len(element.content.strip()) < 50:
                        try:
                            ocr_result = self._ocr_element_region(doc, element)
                            if ocr_result and len(ocr_result['best_text']) > len(element.content):
                                element.content = ocr_result['best_text']
                                element.metadata['ocr_enhanced'] = True
                                element.metadata['ocr_confidence'] = ocr_result['confidence']
                                ocr_results['enhanced_elements'].append(element)
                        except Exception as e:
                            logger.warning(f"OCR enhancement failed for element: {e}")

            doc.close()

            logger.info(f"Enhanced {len(ocr_results['enhanced_elements'])} elements with OCR")
            return ocr_results

        except Exception as e:
            logger.error(f"Multi-OCR enhancement failed: {e}")
            return ocr_results

    def _ocr_element_region(self, doc: fitz.Document, element) -> Optional[Dict[str, Any]]:
        """Perform OCR on a specific element region"""
        try:
            page = doc[element.page_num]

            # Extract region as high-quality image
            bbox = fitz.Rect(element.bbox[0]/2, element.bbox[1]/2, element.bbox[2]/2, element.bbox[3]/2)
            mat = fitz.Matrix(4, 4)  # 4x zoom for better OCR
            pix = page.get_pixmap(matrix=mat, clip=bbox)
            img_data = pix.tobytes("png")

            # Convert to PIL Image and enhance
            image = Image.open(io.BytesIO(img_data))
            enhanced_image = self._enhance_image_for_ocr(image)

            ocr_results = {}

            # Tesseract OCR
            if self.tesseract_available and self.config.use_tesseract:
                try:
                    tesseract_text = pytesseract.image_to_string(
                        enhanced_image,
                        lang='+'.join(self.config.ocr_languages),
                        config=self.config.tesseract_config
                    )
                    tesseract_confidence = self._get_tesseract_confidence(enhanced_image)
                    ocr_results['tesseract'] = {
                        'text': tesseract_text.strip(),
                        'confidence': tesseract_confidence
                    }
                except Exception as e:
                    logger.debug(f"Tesseract OCR failed: {e}")

            # EasyOCR
            if self.easyocr_reader and self.config.use_easyocr:
                try:
                    # Convert PIL to numpy array
                    img_array = np.array(enhanced_image)
                    easyocr_results = self.easyocr_reader.readtext(img_array)

                    easyocr_text = ' '.join([result[1] for result in easyocr_results])
                    easyocr_confidence = np.mean([result[2] for result in easyocr_results]) if easyocr_results else 0

                    ocr_results['easyocr'] = {
                        'text': easyocr_text.strip(),
                        'confidence': easyocr_confidence
                    }
                except Exception as e:
                    logger.debug(f"EasyOCR failed: {e}")

            # Select best OCR result
            if ocr_results:
                best_result = max(ocr_results.values(), key=lambda x: x['confidence'])
                return {
                    'best_text': best_result['text'],
                    'confidence': best_result['confidence'],
                    'all_results': ocr_results
                }

            return None

        except Exception as e:
            logger.debug(f"OCR element region failed: {e}")
            return None

    def _enhance_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """Enhance image quality for better OCR results"""
        try:
            # Convert to grayscale if not already
            if image.mode != 'L':
                image = image.convert('L')

            # Resize if too small (OCR works better on larger images)
            width, height = image.size
            if width < 200 or height < 50:
                scale_factor = max(200/width, 50/height, 2.0)
                new_size = (int(width * scale_factor), int(height * scale_factor))
                image = image.resize(new_size, Image.Resampling.LANCZOS)

            # Enhance contrast
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)

            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(2.0)

            # Apply denoising using OpenCV
            img_array = np.array(image)
            denoised = cv2.fastNlMeansDenoising(img_array)

            # Apply adaptive thresholding for better text separation
            thresh = cv2.adaptiveThreshold(
                denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )

            # Convert back to PIL
            enhanced_image = Image.fromarray(thresh)

            return enhanced_image

        except Exception as e:
            logger.debug(f"Image enhancement failed: {e}")
            return image

    def _get_tesseract_confidence(self, image: Image.Image) -> float:
        """Get confidence score from Tesseract OCR"""
        try:
            data = pytesseract.image_to_data(
                image,
                lang='+'.join(self.config.ocr_languages),
                config=self.config.tesseract_config,
                output_type=pytesseract.Output.DICT
            )

            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            return np.mean(confidences) / 100.0 if confidences else 0.0

        except Exception:
            return 0.5  # Default confidence

    async def _extract_structured_elements(self, file_path: Path, layout_elements: List) -> Dict[str, Any]:
        """Extract structured elements (tables, charts, images)"""
        structured_elements = {
            'images': [],
            'tables': [],
            'charts': []
        }

        try:
            doc = fitz.open(str(file_path))

            for page_num in range(len(doc)):
                page = doc[page_num]

                # Extract images
                images = self._extract_images_from_page(page, page_num)
                structured_elements['images'].extend(images)

                # Extract tables
                tables = self._extract_tables_from_page(doc, page_num)
                structured_elements['tables'].extend(tables)

                # Analyze charts (basic implementation)
                charts = self._analyze_charts_on_page(page, page_num)
                structured_elements['charts'].extend(charts)

            doc.close()

            logger.info(f"Extracted structured elements: {len(structured_elements['images'])} images, "
                       f"{len(structured_elements['tables'])} tables, {len(structured_elements['charts'])} charts")

            return structured_elements

        except Exception as e:
            logger.error(f"Structured element extraction failed: {e}")
            return structured_elements

    def _extract_images_from_page(self, page: fitz.Page, page_num: int) -> List[Dict[str, Any]]:
        """Extract and analyze images from page"""
        images = []

        try:
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)

                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        img_data = pix.tobytes("png")

                        # Basic image analysis (can be enhanced with AI)
                        analysis = f"Image {img_index + 1} on page {page_num + 1}"

                        image_info = {
                            'page': page_num + 1,
                            'index': img_index,
                            'width': pix.width,
                            'height': pix.height,
                            'analysis': analysis,
                            'type': 'image'
                        }
                        images.append(image_info)

                    pix = None  # Free memory

                except Exception as e:
                    logger.warning(f"Failed to extract image {img_index} from page {page_num + 1}: {str(e)}")

        except Exception as e:
            logger.warning(f"Failed to extract images from page {page_num + 1}: {str(e)}")

        return images

    def _extract_tables_from_page(self, doc: fitz.Document, page_num: int) -> List[Dict[str, Any]]:
        """Extract tables from page using multiple methods"""
        tables = []

        try:
            # Method 1: Try Camelot (works well for well-formatted tables)
            try:
                camelot_tables = camelot.read_pdf(
                    str(doc.name),
                    pages=str(page_num + 1),
                    flavor='lattice'
                )

                for i, table in enumerate(camelot_tables):
                    if table.accuracy > 50:  # Only include high-accuracy tables
                        table_data = {
                            'page': page_num + 1,
                            'index': i,
                            'method': 'camelot',
                            'accuracy': table.accuracy,
                            'content': table.df.to_string(),
                            'rows': table.df.shape[0],
                            'columns': table.df.shape[1]
                        }
                        tables.append(table_data)
            except Exception as e:
                logger.debug(f"Camelot table extraction failed for page {page_num + 1}: {str(e)}")

            # Method 2: Try Tabula (works well for stream tables)
            try:
                tabula_tables = tabula.read_pdf(
                    str(doc.name),
                    pages=page_num + 1,
                    multiple_tables=True,
                    pandas_options={'header': 0}
                )

                for i, df in enumerate(tabula_tables):
                    if not df.empty and df.shape[0] > 1:  # Must have data
                        table_data = {
                            'page': page_num + 1,
                            'index': len(tables) + i,
                            'method': 'tabula',
                            'accuracy': 85,  # Tabula doesn't provide accuracy
                            'content': df.to_string(),
                            'rows': df.shape[0],
                            'columns': df.shape[1]
                        }
                        tables.append(table_data)
            except Exception as e:
                logger.debug(f"Tabula table extraction failed for page {page_num + 1}: {str(e)}")

        except Exception as e:
            logger.warning(f"Table extraction failed for page {page_num + 1}: {str(e)}")

        return tables

    def _analyze_charts_on_page(self, page: fitz.Page, page_num: int) -> List[Dict[str, Any]]:
        """Analyze charts and diagrams on page"""
        charts = []

        try:
            # Basic chart detection - can be enhanced with AI
            # For now, just detect if there are images that might be charts
            image_list = page.get_images()

            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(page.parent, xref)

                    # Simple heuristic: larger images might be charts
                    if pix.width > 200 and pix.height > 100:
                        chart_data = {
                            'page': page_num + 1,
                            'index': img_index,
                            'type': 'potential_chart',
                            'description': f'Potential chart or diagram on page {page_num + 1}',
                            'confidence': 0.5,
                            'width': pix.width,
                            'height': pix.height
                        }
                        charts.append(chart_data)

                    pix = None

                except Exception as e:
                    logger.debug(f"Chart analysis failed for image {img_index}: {e}")

        except Exception as e:
            logger.warning(f"Chart analysis failed for page {page_num + 1}: {str(e)}")

        return charts

    async def _ai_enhance_content(self, content_extraction: Dict[str, Any],
                                 structured_elements: Dict[str, Any],
                                 layout_elements: List) -> Dict[str, Any]:
        """AI-powered content enhancement and analysis"""
        enhanced_content = {
            'enhanced_text': content_extraction.get('reading_order_text', ''),
            'key_insights': [],
            'franchise_info': {},
            'content_summary': ''
        }

        try:
            text_content = content_extraction.get('reading_order_text', '')

            if text_content and self.config.use_gpt4_text_enhancement:
                # Extract franchise-specific information
                franchise_info = self._extract_franchise_information(text_content)
                enhanced_content['franchise_info'] = franchise_info

                # Generate content summary
                summary = self._generate_content_summary(text_content)
                enhanced_content['content_summary'] = summary

                # Extract key insights
                insights = self._extract_key_insights(text_content)
                enhanced_content['key_insights'] = insights

            logger.info("AI content enhancement completed")
            return enhanced_content

        except Exception as e:
            logger.error(f"AI content enhancement failed: {e}")
            return enhanced_content

    def _extract_franchise_information(self, text: str) -> Dict[str, Any]:
        """Extract franchise-specific information from text"""
        franchise_info = {
            'financial_info': [],
            'support_info': [],
            'territory_info': [],
            'business_model': []
        }

        # Extract financial information
        import re

        # Look for investment amounts
        investment_patterns = [
            r'investment.*?\$[\d,]+',
            r'franchise fee.*?\$[\d,]+',
            r'total.*?\$[\d,]+',
            r'cost.*?\$[\d,]+'
        ]

        for pattern in investment_patterns:
            matches = re.findall(pattern, text.lower())
            franchise_info['financial_info'].extend(matches[:3])

        # Look for support information
        support_keywords = ['training', 'support', 'assistance', 'help', 'guidance']
        for keyword in support_keywords:
            if keyword in text.lower():
                # Find sentences containing the keyword
                sentences = text.split('.')
                for sentence in sentences:
                    if keyword in sentence.lower():
                        franchise_info['support_info'].append(sentence.strip())
                        break

        # Look for territory information
        territory_keywords = ['territory', 'location', 'area', 'region', 'exclusive']
        for keyword in territory_keywords:
            if keyword in text.lower():
                sentences = text.split('.')
                for sentence in sentences:
                    if keyword in sentence.lower():
                        franchise_info['territory_info'].append(sentence.strip())
                        break

        # Business model information
        business_keywords = ['car wash', 'hydrogreen', 'eco-friendly', 'environmental']
        for keyword in business_keywords:
            if keyword in text.lower():
                sentences = text.split('.')
                for sentence in sentences:
                    if keyword in sentence.lower():
                        franchise_info['business_model'].append(sentence.strip())
                        break

        return franchise_info

    def _generate_content_summary(self, text: str) -> str:
        """Generate a summary of the content"""
        # Simple extractive summary - first few sentences
        sentences = text.split('.')[:5]
        summary = '. '.join(sentence.strip() for sentence in sentences if sentence.strip())
        return summary + '.' if summary else "Document content summary not available."

    def _extract_key_insights(self, text: str) -> List[str]:
        """Extract key insights from the text"""
        insights = []

        # Look for key franchise insights
        if 'investment' in text.lower():
            insights.append("Contains investment information")

        if any(word in text.lower() for word in ['training', 'support']):
            insights.append("Includes training and support details")

        if any(word in text.lower() for word in ['territory', 'location']):
            insights.append("Covers territory and location requirements")

        if any(word in text.lower() for word in ['eco-friendly', 'environmental']):
            insights.append("Emphasizes environmental benefits")

        return insights

    def _create_layout_aware_chunks(self, ai_enhanced_content: Dict[str, Any],
                                   layout_elements: List,
                                   structured_elements: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create layout-aware semantic chunks"""
        chunks = []

        try:
            text_content = ai_enhanced_content.get('enhanced_text', '')

            if not text_content:
                return chunks

            # Use the text splitter for basic chunking
            text_chunks = self.text_splitter.split_text(text_content)

            for i, chunk_text in enumerate(text_chunks):
                chunk = {
                    'content': chunk_text,
                    'chunk_index': i,
                    'type': 'text',
                    'page_numbers': self._extract_page_numbers(chunk_text),
                    'structural_info': {
                        'contains_heading': self._contains_heading(chunk_text),
                        'contains_list': self._contains_list(chunk_text),
                        'contains_financial': self._contains_financial_info(chunk_text),
                        'contains_contact': self._contains_contact_info(chunk_text)
                    },
                    'reading_order': i
                }
                chunks.append(chunk)

            # Add chunks for structured elements
            chunks.extend(self._create_structured_chunks(structured_elements, len(chunks)))

            logger.info(f"Created {len(chunks)} layout-aware chunks")
            return chunks

        except Exception as e:
            logger.error(f"Layout-aware chunking failed: {e}")
            return chunks

    def _extract_page_numbers(self, text: str) -> List[int]:
        """Extract page numbers from text"""
        import re
        page_matches = re.findall(r'PAGE (\d+)', text.upper())
        return [int(page) for page in page_matches]

    def _contains_heading(self, text: str) -> bool:
        """Check if text contains headings"""
        return '===' in text or text.strip().isupper()

    def _contains_list(self, text: str) -> bool:
        """Check if text contains lists"""
        return '•' in text or re.search(r'^\d+\.', text.strip(), re.MULTILINE) is not None

    def _contains_financial_info(self, text: str) -> bool:
        """Check if text contains financial information"""
        return '$' in text or any(word in text.lower() for word in ['investment', 'cost', 'fee', 'price'])

    def _contains_contact_info(self, text: str) -> bool:
        """Check if text contains contact information"""
        import re
        phone_pattern = r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return (bool(re.search(phone_pattern, text)) or
                bool(re.search(email_pattern, text)) or
                'contact' in text.lower())

    def _create_structured_chunks(self, structured_elements: Dict[str, Any], start_index: int) -> List[Dict[str, Any]]:
        """Create chunks for structured elements"""
        chunks = []
        chunk_index = start_index

        # Add table chunks
        for table in structured_elements.get('tables', []):
            chunk = {
                'content': f"TABLE: {table.get('content', '')}",
                'chunk_index': chunk_index,
                'type': 'table',
                'page_numbers': [table.get('page', 1)],
                'structural_info': {
                    'table_rows': table.get('rows', 0),
                    'table_columns': table.get('columns', 0),
                    'extraction_method': table.get('method', 'unknown')
                },
                'reading_order': chunk_index
            }
            chunks.append(chunk)
            chunk_index += 1

        # Add image chunks
        for image in structured_elements.get('images', []):
            chunk = {
                'content': f"IMAGE: {image.get('analysis', '')}",
                'chunk_index': chunk_index,
                'type': 'image',
                'page_numbers': [image.get('page', 1)],
                'structural_info': {
                    'image_width': image.get('width', 0),
                    'image_height': image.get('height', 0),
                    'image_type': image.get('type', 'unknown')
                },
                'reading_order': chunk_index
            }
            chunks.append(chunk)
            chunk_index += 1

        return chunks

    def _extract_comprehensive_metadata(self, file_path: Path,
                                       enhanced_text: str,
                                       layout_elements: List,
                                       structured_elements: Dict[str, Any]) -> Dict[str, Any]:
        """Extract comprehensive metadata"""
        metadata = {
            'filename': file_path.name,
            'file_size': file_path.stat().st_size,
            'processing_info': {
                'layout_elements_detected': len(layout_elements),
                'images_processed': len(structured_elements.get('images', [])),
                'tables_extracted': len(structured_elements.get('tables', [])),
                'charts_analyzed': len(structured_elements.get('charts', [])),
                'text_length': len(enhanced_text),
                'enhanced_processing_used': True
            },
            'content_analysis': {
                'contains_financial_info': self._contains_financial_info(enhanced_text),
                'contains_contact_info': self._contains_contact_info(enhanced_text),
                'contains_franchise_terms': 'franchise' in enhanced_text.lower(),
                'document_type': 'franchise_information' if 'franchise' in enhanced_text.lower() else 'document'
            }
        }

        return metadata
