#!/usr/bin/env python3
"""
LayoutParser Installation Verification Script
"""

import sys
import traceback

def test_imports():
    """Test basic imports"""
    print("🧪 Testing imports...")
    
    try:
        import layoutparser as lp
        print("✅ LayoutParser imported successfully")
        print(f"   Version: {lp.__version__}")
    except ImportError as e:
        print(f"❌ LayoutParser import failed: {e}")
        return False
    
    try:
        import detectron2
        print("✅ Detectron2 imported successfully")
    except ImportError as e:
        print(f"❌ Detectron2 import failed: {e}")
        return False
    
    try:
        import torch
        print("✅ PyTorch imported successfully")
        print(f"   Version: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV imported successfully")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        import pytesseract
        print("✅ PyTesseract imported successfully")
    except ImportError as e:
        print(f"❌ PyTesseract import failed: {e}")
        return False
    
    return True

def test_model_loading():
    """Test loading a LayoutParser model"""
    print("\n🎯 Testing model loading...")
    
    try:
        import layoutparser as lp
        
        # Try to load a pre-trained model
        model = lp.Detectron2LayoutModel(
            'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
            extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
            label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
        )
        print("✅ LayoutParser model loaded successfully")
        print(f"   Model type: {type(model)}")
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        traceback.print_exc()
        return False

def test_sample_detection():
    """Test layout detection on a sample image"""
    print("\n🖼️  Testing layout detection...")
    
    try:
        import layoutparser as lp
        import numpy as np
        from PIL import Image
        
        # Create a simple test image
        test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255
        test_image[50:150, 50:550] = [0, 0, 0]  # Title area
        test_image[200:350, 50:550] = [128, 128, 128]  # Text area
        
        # Convert to PIL Image
        pil_image = Image.fromarray(test_image)
        
        # Load model
        model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')
        
        # Detect layout
        layout = model.detect(pil_image)
        
        print(f"✅ Layout detection successful")
        print(f"   Detected {len(layout)} layout elements")
        
        for i, element in enumerate(layout):
            print(f"   Element {i+1}: {element.type} (confidence: {element.score:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Layout detection failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main verification function"""
    print("🧪 LayoutParser Installation Verification")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed")
        sys.exit(1)
    
    # Test model loading
    if not test_model_loading():
        print("\n⚠️  Model loading failed (this might be due to network issues)")
    
    # Test sample detection
    if not test_sample_detection():
        print("\n⚠️  Sample detection failed")
    
    print("\n🎉 LayoutParser verification completed!")
    print("\n📋 Next steps:")
    print("1. Test with your own PDF documents")
    print("2. Integrate with your document processing pipeline")
    print("3. Explore different pre-trained models available")
    
    print("\n💡 Available models:")
    print("   - lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config")
    print("   - lp://TableBank/faster_rcnn_R_50_FPN_3x/config")
    print("   - lp://NewspaperNavigator/faster_rcnn_R_50_FPN_3x/config")

if __name__ == "__main__":
    main()
