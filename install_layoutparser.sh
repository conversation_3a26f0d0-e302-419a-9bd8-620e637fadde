#!/bin/bash
# LayoutParser Installation Script
# This script installs LayoutParser with all dependencies in a clean virtual environment

set -e  # Exit on any error

echo "🚀 LayoutParser Installation Script"
echo "=================================="

# Detect OS
OS="unknown"
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    OS="windows"
fi

echo "🔍 Detected OS: $OS"

# Check Python version
echo "🐍 Checking Python version..."
python3 --version || { echo "❌ Python 3 not found. Please install Python 3.8+"; exit 1; }

# Create virtual environment
echo "📦 Creating virtual environment 'lp-env'..."
if [ -d "lp-env" ]; then
    echo "⚠️  Virtual environment 'lp-env' already exists. Removing..."
    rm -rf lp-env
fi

python3 -m venv lp-env

# Activate virtual environment
echo "🔄 Activating virtual environment..."
if [[ "$OS" == "windows" ]]; then
    source lp-env/Scripts/activate
else
    source lp-env/bin/activate
fi

# Upgrade pip
echo "⬆️  Upgrading pip..."
pip install --upgrade pip setuptools wheel

# Install system dependencies based on OS
echo "📋 Installing system dependencies..."
case $OS in
    "linux")
        echo "🐧 Installing Linux dependencies..."
        sudo apt-get update
        sudo apt-get install -y tesseract-ocr tesseract-ocr-eng poppler-utils
        sudo apt-get install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1
        ;;
    "macos")
        echo "🍎 Installing macOS dependencies..."
        if command -v brew &> /dev/null; then
            brew install tesseract poppler
        else
            echo "❌ Homebrew not found. Please install Homebrew first:"
            echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
            exit 1
        fi
        ;;
    "windows")
        echo "🪟 Windows detected. Please install manually:"
        echo "   1. Tesseract: https://github.com/tesseract-ocr/tesseract"
        echo "   2. Poppler: https://github.com/oschwartz10612/poppler-windows"
        echo "   3. Add both to your PATH environment variable"
        echo "   Press Enter to continue after installing..."
        read
        ;;
esac

# Install PyTorch (required for Detectron2)
echo "🔥 Installing PyTorch..."
pip install torch torchvision torchaudio

# Install Detectron2
echo "🎯 Installing Detectron2..."
pip install 'git+https://github.com/facebookresearch/detectron2.git'

# Install LayoutParser with layout models
echo "📐 Installing LayoutParser with layout models..."
pip install "layoutparser[layoutmodels,tesseract,paddledetection]"

# Install additional dependencies
echo "📚 Installing additional dependencies..."
pip install pytesseract pdf2image opencv-python pillow numpy

# Verify Tesseract installation
echo "✅ Verifying Tesseract installation..."
tesseract --version || echo "⚠️  Tesseract not found in PATH"

# Create verification script
echo "📝 Creating verification script..."
cat > verify_layoutparser.py << 'EOF'
#!/usr/bin/env python3
"""
LayoutParser Installation Verification Script
"""

import sys
import traceback

def test_imports():
    """Test basic imports"""
    print("🧪 Testing imports...")
    
    try:
        import layoutparser as lp
        print("✅ LayoutParser imported successfully")
        print(f"   Version: {lp.__version__}")
    except ImportError as e:
        print(f"❌ LayoutParser import failed: {e}")
        return False
    
    try:
        import detectron2
        print("✅ Detectron2 imported successfully")
    except ImportError as e:
        print(f"❌ Detectron2 import failed: {e}")
        return False
    
    try:
        import torch
        print("✅ PyTorch imported successfully")
        print(f"   Version: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV imported successfully")
    except ImportError as e:
        print(f"❌ OpenCV import failed: {e}")
        return False
    
    try:
        import pytesseract
        print("✅ PyTesseract imported successfully")
    except ImportError as e:
        print(f"❌ PyTesseract import failed: {e}")
        return False
    
    return True

def test_model_loading():
    """Test loading a LayoutParser model"""
    print("\n🎯 Testing model loading...")
    
    try:
        import layoutparser as lp
        
        # Try to load a pre-trained model
        model = lp.Detectron2LayoutModel(
            'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
            extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
            label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
        )
        print("✅ LayoutParser model loaded successfully")
        print(f"   Model type: {type(model)}")
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        traceback.print_exc()
        return False

def test_sample_detection():
    """Test layout detection on a sample image"""
    print("\n🖼️  Testing layout detection...")
    
    try:
        import layoutparser as lp
        import numpy as np
        from PIL import Image
        
        # Create a simple test image
        test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255
        test_image[50:150, 50:550] = [0, 0, 0]  # Title area
        test_image[200:350, 50:550] = [128, 128, 128]  # Text area
        
        # Convert to PIL Image
        pil_image = Image.fromarray(test_image)
        
        # Load model
        model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')
        
        # Detect layout
        layout = model.detect(pil_image)
        
        print(f"✅ Layout detection successful")
        print(f"   Detected {len(layout)} layout elements")
        
        for i, element in enumerate(layout):
            print(f"   Element {i+1}: {element.type} (confidence: {element.score:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Layout detection failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main verification function"""
    print("🧪 LayoutParser Installation Verification")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("\n❌ Import tests failed")
        sys.exit(1)
    
    # Test model loading
    if not test_model_loading():
        print("\n⚠️  Model loading failed (this might be due to network issues)")
    
    # Test sample detection
    if not test_sample_detection():
        print("\n⚠️  Sample detection failed")
    
    print("\n🎉 LayoutParser verification completed!")
    print("\n📋 Next steps:")
    print("1. Test with your own PDF documents")
    print("2. Integrate with your document processing pipeline")
    print("3. Explore different pre-trained models available")
    
    print("\n💡 Available models:")
    print("   - lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config")
    print("   - lp://TableBank/faster_rcnn_R_50_FPN_3x/config")
    print("   - lp://NewspaperNavigator/faster_rcnn_R_50_FPN_3x/config")

if __name__ == "__main__":
    main()
EOF

# Make verification script executable
chmod +x verify_layoutparser.py

# Run verification
echo "🧪 Running verification tests..."
python verify_layoutparser.py

echo ""
echo "🎉 LayoutParser installation completed!"
echo ""
echo "📋 Summary:"
echo "   ✅ Virtual environment 'lp-env' created"
echo "   ✅ LayoutParser with layout models installed"
echo "   ✅ Detectron2 installed"
echo "   ✅ PyTorch installed"
echo "   ✅ OCR dependencies installed"
echo ""
echo "🚀 To use LayoutParser:"
echo "   1. Activate environment: source lp-env/bin/activate"
echo "   2. Run your Python scripts with LayoutParser"
echo ""
echo "📖 Example usage:"
echo "   import layoutparser as lp"
echo "   model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')"
echo "   layout = model.detect(image)"
echo ""
echo "🔧 To integrate with your project:"
echo "   pip install -r requirements.txt  # Install your project dependencies"
echo "   # Then use LayoutParser in your enhanced document processing"
