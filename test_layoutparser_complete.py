#!/usr/bin/env python3
"""
Complete LayoutParser Test and Verification Script

This script provides comprehensive testing of LayoutParser installation
and demonstrates various features including layout detection, OCR integration,
and document processing capabilities.
"""

import sys
import os
import traceback
import time
from pathlib import Path
from typing import List, Dict, Any

def test_basic_imports():
    """Test basic imports and versions"""
    print("🧪 Testing Basic Imports")
    print("=" * 40)
    
    imports_status = {}
    
    # Test LayoutParser
    try:
        import layoutparser as lp
        print(f"✅ LayoutParser: {lp.__version__}")
        imports_status['layoutparser'] = True
    except ImportError as e:
        print(f"❌ LayoutParser: {e}")
        imports_status['layoutparser'] = False
    
    # Test Detectron2
    try:
        import detectron2
        from detectron2 import __version__ as d2_version
        print(f"✅ Detectron2: {d2_version}")
        imports_status['detectron2'] = True
    except ImportError as e:
        print(f"❌ Detectron2: {e}")
        imports_status['detectron2'] = False
    
    # Test PyTorch
    try:
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        print(f"   CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"   CUDA devices: {torch.cuda.device_count()}")
        imports_status['torch'] = True
    except ImportError as e:
        print(f"❌ PyTorch: {e}")
        imports_status['torch'] = False
    
    # Test OpenCV
    try:
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
        imports_status['opencv'] = True
    except ImportError as e:
        print(f"❌ OpenCV: {e}")
        imports_status['opencv'] = False
    
    # Test PIL
    try:
        from PIL import Image
        print(f"✅ PIL/Pillow: Available")
        imports_status['pil'] = True
    except ImportError as e:
        print(f"❌ PIL/Pillow: {e}")
        imports_status['pil'] = False
    
    # Test PyTesseract
    try:
        import pytesseract
        print(f"✅ PyTesseract: Available")
        # Test Tesseract binary
        try:
            version = pytesseract.get_tesseract_version()
            print(f"   Tesseract version: {version}")
            imports_status['tesseract'] = True
        except Exception as e:
            print(f"   ⚠️  Tesseract binary issue: {e}")
            imports_status['tesseract'] = False
    except ImportError as e:
        print(f"❌ PyTesseract: {e}")
        imports_status['tesseract'] = False
    
    return imports_status

def test_model_loading():
    """Test loading different LayoutParser models"""
    print("\n🎯 Testing Model Loading")
    print("=" * 40)
    
    try:
        import layoutparser as lp
        
        models_to_test = [
            {
                'name': 'PubLayNet',
                'config': 'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
                'labels': {0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
            },
            {
                'name': 'TableBank',
                'config': 'lp://TableBank/faster_rcnn_R_50_FPN_3x/config',
                'labels': {0: "Table"}
            }
        ]
        
        loaded_models = {}
        
        for model_info in models_to_test:
            try:
                print(f"📥 Loading {model_info['name']} model...")
                start_time = time.time()
                
                model = lp.Detectron2LayoutModel(
                    model_info['config'],
                    extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
                    label_map=model_info['labels']
                )
                
                load_time = time.time() - start_time
                print(f"✅ {model_info['name']} loaded successfully ({load_time:.2f}s)")
                loaded_models[model_info['name']] = model
                
            except Exception as e:
                print(f"❌ {model_info['name']} loading failed: {e}")
                loaded_models[model_info['name']] = None
        
        return loaded_models
        
    except Exception as e:
        print(f"❌ Model loading test failed: {e}")
        return {}

def create_test_image():
    """Create a test image with different layout elements"""
    try:
        import numpy as np
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a white canvas
        width, height = 800, 600
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # Try to use a default font
        try:
            font_large = ImageFont.truetype("arial.ttf", 24)
            font_medium = ImageFont.truetype("arial.ttf", 16)
            font_small = ImageFont.truetype("arial.ttf", 12)
        except:
            # Fallback to default font
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Draw title
        draw.rectangle([50, 50, 750, 100], fill='lightblue', outline='blue')
        draw.text((60, 65), "DOCUMENT TITLE", fill='black', font=font_large)
        
        # Draw text blocks
        draw.rectangle([50, 120, 350, 250], fill='lightgray', outline='gray')
        draw.text((60, 130), "Text Block 1", fill='black', font=font_medium)
        draw.text((60, 150), "This is sample text content", fill='black', font=font_small)
        draw.text((60, 170), "that would appear in a", fill='black', font=font_small)
        draw.text((60, 190), "typical document layout.", fill='black', font=font_small)
        
        draw.rectangle([400, 120, 750, 250], fill='lightgray', outline='gray')
        draw.text((410, 130), "Text Block 2", fill='black', font=font_medium)
        draw.text((410, 150), "Another text section with", fill='black', font=font_small)
        draw.text((410, 170), "different content and", fill='black', font=font_small)
        draw.text((410, 190), "layout structure.", fill='black', font=font_small)
        
        # Draw a table
        draw.rectangle([50, 280, 750, 450], fill='lightyellow', outline='orange')
        draw.text((60, 290), "TABLE", fill='black', font=font_medium)
        
        # Table grid
        for i in range(4):
            y = 320 + i * 30
            draw.line([60, y, 740, y], fill='black')
        for i in range(4):
            x = 60 + i * 170
            draw.line([x, 320, x, 440], fill='black')
        
        # Draw a figure/chart area
        draw.rectangle([50, 470, 350, 550], fill='lightgreen', outline='green')
        draw.text((60, 480), "FIGURE/CHART", fill='black', font=font_medium)
        draw.ellipse([100, 500, 300, 540], fill='green', outline='darkgreen')
        
        # Draw a list
        draw.rectangle([400, 470, 750, 550], fill='lightcoral', outline='red')
        draw.text((410, 480), "LIST", fill='black', font=font_medium)
        draw.text((410, 500), "• Item 1", fill='black', font=font_small)
        draw.text((410, 515), "• Item 2", fill='black', font=font_small)
        draw.text((410, 530), "• Item 3", fill='black', font=font_small)
        
        return image
        
    except Exception as e:
        print(f"❌ Test image creation failed: {e}")
        return None

def test_layout_detection(models: Dict[str, Any]):
    """Test layout detection on sample images"""
    print("\n🖼️  Testing Layout Detection")
    print("=" * 40)
    
    if not models:
        print("❌ No models available for testing")
        return False
    
    # Create test image
    test_image = create_test_image()
    if test_image is None:
        print("❌ Could not create test image")
        return False
    
    print("✅ Test image created")
    
    # Test with available models
    for model_name, model in models.items():
        if model is None:
            continue
            
        try:
            print(f"\n🔍 Testing {model_name} model...")
            start_time = time.time()
            
            # Detect layout
            layout = model.detect(test_image)
            
            detection_time = time.time() - start_time
            print(f"✅ Detection completed ({detection_time:.2f}s)")
            print(f"   Found {len(layout)} layout elements:")
            
            for i, element in enumerate(layout):
                bbox = element.block
                print(f"   {i+1}. {element.type} (confidence: {element.score:.3f}) "
                      f"at [{bbox.x_1:.0f}, {bbox.y_1:.0f}, {bbox.x_2:.0f}, {bbox.y_2:.0f}]")
            
        except Exception as e:
            print(f"❌ {model_name} detection failed: {e}")
            traceback.print_exc()
    
    return True

def test_ocr_integration():
    """Test OCR integration with LayoutParser"""
    print("\n📝 Testing OCR Integration")
    print("=" * 40)
    
    try:
        import layoutparser as lp
        import pytesseract
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a simple text image
        image = Image.new('RGB', (400, 200), 'white')
        draw = ImageDraw.Draw(image)
        
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        draw.text((20, 50), "This is a test document", fill='black', font=font)
        draw.text((20, 80), "with multiple lines of text", fill='black', font=font)
        draw.text((20, 110), "for OCR testing.", fill='black', font=font)
        
        # Test OCR
        ocr_text = pytesseract.image_to_string(image)
        print(f"✅ OCR extracted text:")
        print(f"   '{ocr_text.strip()}'")
        
        # Test LayoutParser OCR agent
        try:
            ocr_agent = lp.TesseractAgent(languages='eng')
            lp_ocr_text = ocr_agent.detect(image)
            print(f"✅ LayoutParser OCR:")
            print(f"   '{lp_ocr_text.strip()}'")
        except Exception as e:
            print(f"⚠️  LayoutParser OCR agent failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ OCR integration test failed: {e}")
        return False

def test_pdf_processing():
    """Test PDF processing capabilities"""
    print("\n📄 Testing PDF Processing")
    print("=" * 40)
    
    try:
        import layoutparser as lp
        from pdf2image import convert_from_path
        import tempfile
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter
        
        # Create a simple test PDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_pdf:
            c = canvas.Canvas(tmp_pdf.name, pagesize=letter)
            c.drawString(100, 750, "Test Document Title")
            c.drawString(100, 700, "This is a test PDF document")
            c.drawString(100, 650, "created for LayoutParser testing.")
            
            # Draw a simple table
            c.drawString(100, 600, "Sample Table:")
            c.rect(100, 550, 400, 100)
            c.line(100, 575, 500, 575)
            c.line(300, 550, 300, 650)
            
            c.save()
            
            print(f"✅ Test PDF created: {tmp_pdf.name}")
            
            # Convert PDF to images
            try:
                images = convert_from_path(tmp_pdf.name)
                print(f"✅ PDF converted to {len(images)} image(s)")
                
                # Test layout detection on PDF page
                if images and len(images) > 0:
                    # Load a model for testing
                    try:
                        model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')
                        layout = model.detect(images[0])
                        print(f"✅ Layout detection on PDF: {len(layout)} elements found")
                        
                        for i, element in enumerate(layout):
                            print(f"   {i+1}. {element.type} (confidence: {element.score:.3f})")
                            
                    except Exception as e:
                        print(f"⚠️  Layout detection on PDF failed: {e}")
                
            except Exception as e:
                print(f"❌ PDF to image conversion failed: {e}")
            
            # Clean up
            os.unlink(tmp_pdf.name)
            
        return True
        
    except Exception as e:
        print(f"❌ PDF processing test failed: {e}")
        return False

def generate_usage_examples():
    """Generate usage examples and documentation"""
    print("\n📖 Usage Examples")
    print("=" * 40)
    
    examples = """
# Basic LayoutParser Usage

## 1. Load a model and detect layout
```python
import layoutparser as lp

# Load pre-trained model
model = lp.Detectron2LayoutModel(
    'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
    extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
    label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
)

# Load image
image = lp.io.load_image("document.png")

# Detect layout
layout = model.detect(image)

# Print results
for element in layout:
    print(f"{element.type}: {element.block} (confidence: {element.score})")
```

## 2. OCR Integration
```python
import layoutparser as lp

# Create OCR agent
ocr_agent = lp.TesseractAgent(languages='eng')

# Extract text from image
text = ocr_agent.detect(image)
print(text)
```

## 3. PDF Processing
```python
import layoutparser as lp
from pdf2image import convert_from_path

# Convert PDF to images
images = convert_from_path("document.pdf")

# Process each page
for i, image in enumerate(images):
    layout = model.detect(image)
    print(f"Page {i+1}: {len(layout)} elements")
```

## 4. Advanced Pipeline
```python
import layoutparser as lp

# Create processing pipeline
model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')
ocr_agent = lp.TesseractAgent()

# Process document
image = lp.io.load_image("document.png")
layout = model.detect(image)

# Extract text from each element
for element in layout:
    if element.type == "Text":
        # Crop the text region
        text_image = element.crop_image(image)
        # Extract text with OCR
        text = ocr_agent.detect(text_image)
        print(f"Text element: {text}")
```

## 5. Available Models
- PubLayNet: lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config
- TableBank: lp://TableBank/faster_rcnn_R_50_FPN_3x/config
- NewspaperNavigator: lp://NewspaperNavigator/faster_rcnn_R_50_FPN_3x/config
- PrimaLayout: lp://PrimaLayout/mask_rcnn_R_50_FPN_3x/config
"""
    
    print(examples)
    
    # Save examples to file
    with open("layoutparser_examples.md", "w") as f:
        f.write(examples)
    
    print("✅ Usage examples saved to 'layoutparser_examples.md'")

def main():
    """Main test function"""
    print("🚀 LayoutParser Complete Test Suite")
    print("=" * 50)
    
    # Test basic imports
    imports_status = test_basic_imports()
    
    if not imports_status.get('layoutparser', False):
        print("\n❌ LayoutParser not available. Please install it first.")
        sys.exit(1)
    
    # Test model loading
    models = test_model_loading()
    
    # Test layout detection
    test_layout_detection(models)
    
    # Test OCR integration
    if imports_status.get('tesseract', False):
        test_ocr_integration()
    else:
        print("\n⚠️  Skipping OCR tests (Tesseract not available)")
    
    # Test PDF processing
    try:
        import pdf2image
        import reportlab
        test_pdf_processing()
    except ImportError:
        print("\n⚠️  Skipping PDF tests (pdf2image or reportlab not available)")
        print("   Install with: pip install pdf2image reportlab")
    
    # Generate usage examples
    generate_usage_examples()
    
    print("\n🎉 LayoutParser test suite completed!")
    print("\n📋 Summary:")
    print(f"   LayoutParser: {'✅' if imports_status.get('layoutparser') else '❌'}")
    print(f"   Detectron2: {'✅' if imports_status.get('detectron2') else '❌'}")
    print(f"   PyTorch: {'✅' if imports_status.get('torch') else '❌'}")
    print(f"   OpenCV: {'✅' if imports_status.get('opencv') else '❌'}")
    print(f"   Tesseract: {'✅' if imports_status.get('tesseract') else '❌'}")
    print(f"   Models loaded: {len([m for m in models.values() if m is not None])}")
    
    if all([imports_status.get('layoutparser'), imports_status.get('detectron2'), 
            imports_status.get('torch'), imports_status.get('opencv')]):
        print("\n🎉 LayoutParser is ready for use!")
    else:
        print("\n⚠️  Some components are missing. Check the installation.")

if __name__ == "__main__":
    main()
