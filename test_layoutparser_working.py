#!/usr/bin/env python3
"""
LayoutParser Working Test

This script properly tests LayoutParser with PyTorch compatibility fixes
and ensures it's actually working for document processing.
"""

import os
import sys
import warnings
from pathlib import Path

# Suppress warnings
warnings.filterwarnings('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'

def setup_pytorch_compatibility():
    """Setup PyTorch compatibility for LayoutParser"""
    import torch
    
    # Store original torch.load
    original_torch_load = torch.load
    
    def patched_torch_load(*args, **kwargs):
        """Patched torch.load that handles weights_only parameter"""
        if 'weights_only' not in kwargs:
            kwargs['weights_only'] = False
        return original_torch_load(*args, **kwargs)
    
    # Monkey patch torch.load
    torch.load = patched_torch_load
    print("✅ PyTorch compatibility patch applied")

def test_layoutparser_installation():
    """Test LayoutParser installation and model loading"""
    print("🔍 Testing LayoutParser Installation")
    print("=" * 50)
    
    try:
        # Setup compatibility first
        setup_pytorch_compatibility()
        
        # Import LayoutParser
        import layoutparser as lp
        print(f"✅ LayoutParser imported: {lp.__version__}")
        
        # Import required dependencies
        import detectron2
        print("✅ Detectron2 available")
        
        import torch
        print(f"✅ PyTorch: {torch.__version__}")
        
        import cv2
        print(f"✅ OpenCV: {cv2.__version__}")
        
        return True
        
    except Exception as e:
        print(f"❌ LayoutParser installation test failed: {e}")
        return False

def test_model_loading_with_fallback():
    """Test model loading with fallback options"""
    print("\n🎯 Testing Model Loading")
    print("=" * 50)
    
    try:
        import layoutparser as lp
        
        # Try different models in order of reliability
        models_to_try = [
            {
                'name': 'PubLayNet (Faster R-CNN)',
                'config': 'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
                'labels': {0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
            },
            {
                'name': 'PubLayNet (Mask R-CNN)', 
                'config': 'lp://PubLayNet/mask_rcnn_R_50_FPN_3x/config',
                'labels': {0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
            }
        ]
        
        for model_info in models_to_try:
            try:
                print(f"📥 Trying {model_info['name']}...")
                
                model = lp.Detectron2LayoutModel(
                    model_info['config'],
                    extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
                    label_map=model_info['labels']
                )
                
                print(f"✅ {model_info['name']} loaded successfully!")
                return model, model_info['name']
                
            except Exception as e:
                print(f"❌ {model_info['name']} failed: {str(e)[:100]}...")
                continue
        
        print("❌ All models failed to load")
        return None, None
        
    except Exception as e:
        print(f"❌ Model loading test failed: {e}")
        return None, None

def test_layout_detection():
    """Test layout detection on a sample image"""
    print("\n🖼️  Testing Layout Detection")
    print("=" * 50)
    
    try:
        import layoutparser as lp
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
        
        # Load model
        model, model_name = test_model_loading_with_fallback()
        if model is None:
            print("❌ No working model available for testing")
            return False
        
        print(f"🎯 Using {model_name} for detection")
        
        # Create a test document image
        width, height = 800, 600
        image = Image.new('RGB', (width, height), 'white')
        draw = ImageDraw.Draw(image)
        
        # Try to use a font
        try:
            font_large = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 24)
            font_medium = ImageFont.truetype("/System/Library/Fonts/Arial.ttf", 16)
        except:
            font_large = ImageFont.load_default()
            font_medium = ImageFont.load_default()
        
        # Draw document elements
        # Title
        draw.rectangle([50, 50, 750, 100], fill='lightblue', outline='blue', width=2)
        draw.text((60, 65), "DOCUMENT TITLE", fill='black', font=font_large)
        
        # Text blocks
        draw.rectangle([50, 120, 350, 250], fill='lightgray', outline='gray', width=2)
        draw.text((60, 130), "Text Block 1", fill='black', font=font_medium)
        
        draw.rectangle([400, 120, 750, 250], fill='lightgray', outline='gray', width=2)
        draw.text((410, 130), "Text Block 2", fill='black', font=font_medium)
        
        # Table
        draw.rectangle([50, 280, 750, 400], fill='lightyellow', outline='orange', width=2)
        draw.text((60, 290), "TABLE", fill='black', font=font_medium)
        
        # Figure
        draw.rectangle([50, 420, 350, 550], fill='lightgreen', outline='green', width=2)
        draw.text((60, 430), "FIGURE", fill='black', font=font_medium)
        
        # List
        draw.rectangle([400, 420, 750, 550], fill='lightcoral', outline='red', width=2)
        draw.text((410, 430), "LIST", fill='black', font=font_medium)
        
        print("✅ Test image created")
        
        # Detect layout
        print("🔍 Running layout detection...")
        layout = model.detect(image)
        
        print(f"✅ Layout detection completed!")
        print(f"   Found {len(layout)} layout elements:")
        
        # Analyze results
        element_types = {}
        for element in layout:
            element_type = element.type
            element_types[element_type] = element_types.get(element_type, 0) + 1
            
            bbox = element.block
            print(f"   - {element_type}: confidence {element.score:.3f}, "
                  f"bbox [{bbox.x_1:.0f}, {bbox.y_1:.0f}, {bbox.x_2:.0f}, {bbox.y_2:.0f}]")
        
        print(f"\n📊 Element Summary:")
        for elem_type, count in element_types.items():
            print(f"   {elem_type}: {count}")
        
        # Save test image for reference
        image.save("layoutparser_test_image.png")
        print("💾 Test image saved as 'layoutparser_test_image.png'")
        
        return len(layout) > 0
        
    except Exception as e:
        print(f"❌ Layout detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_pdf():
    """Test LayoutParser with the actual Coochie PDF"""
    print("\n📄 Testing with Real PDF")
    print("=" * 50)
    
    pdf_path = "Coochie_Information pack.pdf"
    if not os.path.exists(pdf_path):
        print(f"❌ PDF not found: {pdf_path}")
        return False
    
    try:
        import layoutparser as lp
        from pdf2image import convert_from_path
        
        # Load model
        model, model_name = test_model_loading_with_fallback()
        if model is None:
            print("❌ No working model available")
            return False
        
        print(f"🎯 Using {model_name} for PDF analysis")
        
        # Convert first page to image
        print("📄 Converting PDF to image...")
        images = convert_from_path(pdf_path, first_page=1, last_page=1)
        
        if not images:
            print("❌ Failed to convert PDF to image")
            return False
        
        image = images[0]
        print(f"✅ PDF page converted: {image.size}")
        
        # Detect layout
        print("🔍 Analyzing document layout...")
        layout = model.detect(image)
        
        print(f"✅ Layout analysis completed!")
        print(f"   Found {len(layout)} layout elements on first page:")
        
        # Analyze results
        element_types = {}
        for element in layout:
            element_type = element.type
            element_types[element_type] = element_types.get(element_type, 0) + 1
        
        for elem_type, count in element_types.items():
            print(f"   {elem_type}: {count}")
        
        # Show top elements by confidence
        sorted_elements = sorted(layout, key=lambda x: x.score, reverse=True)
        print(f"\n🏆 Top 5 detected elements:")
        for i, element in enumerate(sorted_elements[:5], 1):
            bbox = element.block
            print(f"   {i}. {element.type} (confidence: {element.score:.3f}) "
                  f"at [{bbox.x_1:.0f}, {bbox.y_1:.0f}, {bbox.x_2:.0f}, {bbox.y_2:.0f}]")
        
        return len(layout) > 0
        
    except Exception as e:
        print(f"❌ PDF test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🚀 LayoutParser Working Test")
    print("=" * 70)
    
    # Test 1: Installation
    if not test_layoutparser_installation():
        print("\n❌ LayoutParser installation failed")
        return False
    
    # Test 2: Layout Detection
    if not test_layout_detection():
        print("\n❌ Layout detection failed")
        return False
    
    # Test 3: Real PDF
    if not test_with_real_pdf():
        print("\n⚠️  Real PDF test failed (but basic functionality works)")
    
    print("\n🎉 LayoutParser is working correctly!")
    print("\n📋 Summary:")
    print("   ✅ LayoutParser installed and importable")
    print("   ✅ Models can be loaded with compatibility fixes")
    print("   ✅ Layout detection working on test images")
    print("   ✅ Ready for integration with enhanced document processing")
    
    print("\n🚀 Next: Run enhanced processing with LayoutParser enabled!")
    
    return True

if __name__ == "__main__":
    # Make sure we're in the LayoutParser environment
    if 'lp-env' not in sys.prefix:
        print("❌ Please activate the LayoutParser environment first:")
        print("   source lp-env/bin/activate")
        print("   python test_layoutparser_working.py")
        sys.exit(1)
    
    success = main()
    if not success:
        sys.exit(1)
