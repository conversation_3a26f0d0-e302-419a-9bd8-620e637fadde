#!/usr/bin/env python3
"""
Final Detectron2 PyTorch Compatibility Patch
"""

import os
import sys
from pathlib import Path

def patch_detectron2_final():
    """Apply the final patch to Detectron2 for PyTorch 2.7+ compatibility"""
    
    try:
        import detectron2
        detectron2_path = Path(detectron2.__file__).parent
        checkpoint_file = detectron2_path / "checkpoint" / "detection_checkpoint.py"
        
        print(f"📝 Patching Detectron2 checkpoint file: {checkpoint_file}")
        
        # Read the current file
        with open(checkpoint_file, 'r') as f:
            content = f.read()
        
        # Check if already patched
        if 'weights_only=False' in content:
            print("✅ Detectron2 already patched")
            return True
        
        # Apply the patch - target the specific line
        original_line = 'data = torch.load(f)'
        patched_line = 'data = torch.load(f, weights_only=False)'
        
        if original_line in content:
            content = content.replace(original_line, patched_line)
            
            # Write the patched file
            with open(checkpoint_file, 'w') as f:
                f.write(content)
            
            print("✅ Detectron2 checkpoint patched successfully")
            return True
        else:
            print("⚠️  Could not find the line to patch in Detectron2")
            print("Content preview:")
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'torch.load' in line:
                    print(f"Line {i+1}: {line.strip()}")
            return False
            
    except Exception as e:
        print(f"❌ Failed to patch Detectron2: {e}")
        return False

def test_layoutparser_final():
    """Final test of LayoutParser functionality"""
    print("\n🧪 Testing LayoutParser after final patch...")
    
    try:
        import layoutparser as lp
        import numpy as np
        from PIL import Image
        
        # Create a simple test image
        test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255
        test_image[50:150, 50:550] = [0, 0, 0]  # Title area
        test_image[200:350, 50:550] = [128, 128, 128]  # Text area
        
        # Convert to PIL Image
        pil_image = Image.fromarray(test_image)
        
        print("📥 Loading LayoutParser model...")
        
        # Load model
        model = lp.Detectron2LayoutModel(
            'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
            extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
            label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
        )
        
        print("✅ Model loaded successfully!")
        
        # Test layout detection
        print("🔍 Testing layout detection...")
        layout = model.detect(pil_image)
        
        print(f"✅ Layout detection successful!")
        print(f"   Detected {len(layout)} layout elements")
        
        for i, element in enumerate(layout):
            print(f"   Element {i+1}: {element.type} (confidence: {element.score:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ LayoutParser test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🔧 Final Detectron2 PyTorch Compatibility Patch")
    print("=" * 50)
    
    # Check if we're in the virtual environment
    if 'lp-env' not in sys.prefix:
        print("⚠️  Please activate the lp-env virtual environment first:")
        print("   source lp-env/bin/activate")
        sys.exit(1)
    
    print("✅ Virtual environment detected")
    
    # Apply the final patch
    if patch_detectron2_final():
        print("\n✅ Detectron2 patch applied successfully")
        
        # Test LayoutParser
        if test_layoutparser_final():
            print("\n🎉 LayoutParser is now working correctly!")
            print("\n📋 You can now use LayoutParser normally:")
            print("   import layoutparser as lp")
            print("   model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')")
            print("   layout = model.detect(image)")
        else:
            print("\n❌ LayoutParser test still failed")
    else:
        print("\n❌ Detectron2 patch failed")

if __name__ == "__main__":
    main()
