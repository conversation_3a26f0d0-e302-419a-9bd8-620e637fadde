
# Basic LayoutParser Usage

## 1. Load a model and detect layout
```python
import layoutparser as lp

# Load pre-trained model
model = lp.Detectron2LayoutModel(
    'lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config',
    extra_config=["MODEL.ROI_HEADS.SCORE_THRESH_TEST", 0.8],
    label_map={0: "Text", 1: "Title", 2: "List", 3: "Table", 4: "Figure"}
)

# Load image
image = lp.io.load_image("document.png")

# Detect layout
layout = model.detect(image)

# Print results
for element in layout:
    print(f"{element.type}: {element.block} (confidence: {element.score})")
```

## 2. OCR Integration
```python
import layoutparser as lp

# Create OCR agent
ocr_agent = lp.TesseractAgent(languages='eng')

# Extract text from image
text = ocr_agent.detect(image)
print(text)
```

## 3. PDF Processing
```python
import layoutparser as lp
from pdf2image import convert_from_path

# Convert PDF to images
images = convert_from_path("document.pdf")

# Process each page
for i, image in enumerate(images):
    layout = model.detect(image)
    print(f"Page {i+1}: {len(layout)} elements")
```

## 4. Advanced Pipeline
```python
import layoutparser as lp

# Create processing pipeline
model = lp.Detectron2LayoutModel('lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config')
ocr_agent = lp.TesseractAgent()

# Process document
image = lp.io.load_image("document.png")
layout = model.detect(image)

# Extract text from each element
for element in layout:
    if element.type == "Text":
        # Crop the text region
        text_image = element.crop_image(image)
        # Extract text with OCR
        text = ocr_agent.detect(text_image)
        print(f"Text element: {text}")
```

## 5. Available Models
- PubLayNet: lp://PubLayNet/faster_rcnn_R_50_FPN_3x/config
- TableBank: lp://TableBank/faster_rcnn_R_50_FPN_3x/config
- NewspaperNavigator: lp://NewspaperNavigator/faster_rcnn_R_50_FPN_3x/config
- PrimaLayout: lp://PrimaLayout/mask_rcnn_R_50_FPN_3x/config
