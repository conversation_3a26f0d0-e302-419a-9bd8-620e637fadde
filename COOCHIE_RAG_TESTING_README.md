# Coochie RAG Testing Suite

This testing suite validates the complete RAG (Retrieval-Augmented Generation) and AI functionality using the **Coochie Information Pack PDF** file. It tests document processing, vector embeddings, question answering, and multi-agent system integration.

## 📁 Test Files

### 1. `run_coochie_rag_tests.py` - Main Test Runner
**Recommended starting point** - Interactive test runner with multiple options:

```bash
# Interactive menu
python run_coochie_rag_tests.py

# Quick validation (30 seconds)
python run_coochie_rag_tests.py quick

# Simple test (5-10 minutes)
python run_coochie_rag_tests.py simple

# Comprehensive test (15-20 minutes)
python run_coochie_rag_tests.py comprehensive
```

### 2. `test_coochie_rag_simple.py` - Simple RAG Test
Focused test covering core functionality:
- PDF upload and processing
- Document-specific questions
- General RAG questions
- Agent system integration

```bash
python test_coochie_rag_simple.py
```

### 3. `test_coochie_rag_comprehensive.py` - Comprehensive Test Suite
Complete system validation including:
- System health checks
- Document processing validation
- Multiple question types
- Agent workflow testing
- Webhook integration
- Edge case handling

```bash
python test_coochie_rag_comprehensive.py
```

## 🚀 Quick Start

1. **Ensure Prerequisites:**
   ```bash
   # Make sure the server is running
   python start_server.py
   
   # Verify the PDF file exists
   ls -la "Coochie_Information pack.pdf"
   ```

2. **Run Quick Validation:**
   ```bash
   python run_coochie_rag_tests.py quick
   ```

3. **Run Full Test:**
   ```bash
   python run_coochie_rag_tests.py simple
   ```

## 📋 What Gets Tested

### Core RAG Functionality
- ✅ PDF document upload and processing
- ✅ Vector embedding generation
- ✅ Document chunking and storage
- ✅ Similarity search and retrieval
- ✅ Context-aware answer generation

### Document-Specific Questions
- "What are the investment requirements for Coochie Hydrogreen?"
- "What franchise fees does Coochie Hydrogreen charge?"
- "What support does Coochie Hydrogreen provide?"
- "What are the territory requirements?"
- "Is this an eco-friendly business?"

### General RAG Questions
- "What franchise opportunities are available?"
- "Tell me about eco-friendly car wash franchises"
- "What are the costs for car wash franchises?"
- "Which franchises are environmentally friendly?"

### Multi-Agent System
- Conversation flow management
- Intent detection and routing
- Lead qualification workflows
- Meeting booking integration
- Context preservation across interactions

### Integration Points
- Document upload API (`/api/documents/upload`)
- DocQA endpoints (`/api/docqa/ask`, `/api/docqa/ask/document/{id}`)
- Agent chat API (`/api/agents/chat`)
- Webhook integration (`/api/webhooks/webhooks/kudosity`)

## 🔧 Configuration

### Environment Variables
The tests use these default configurations:
```python
BASE_URL = "http://localhost:8000/api"
LOGIN_EMAIL = "<EMAIL>"
LOGIN_PASSWORD = "Admin@1234"
PDF_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
```

### Customization
To use different credentials or paths, modify the configuration section in each test file:

```python
# In test files, update these variables:
BASE_URL = "your_server_url"
LOGIN_EMAIL = "your_email"
LOGIN_PASSWORD = "your_password"
PDF_PATH = "path/to/your/pdf"
```

## 📊 Expected Results

### Successful Test Output
```
🎉 All tests passed! RAG system is working correctly.

📊 Test Results:
   Document Questions: ✅ PASSED
   General RAG Questions: ✅ PASSED
   Agent System: ✅ PASSED

📄 Document Information:
   PDF File: Coochie_Information pack.pdf
   Document ID: 12345678-1234-1234-1234-123456789012
```

### What to Look For
1. **Document Upload:** PDF successfully uploaded and processed
2. **Question Answering:** Relevant answers containing Coochie Hydrogreen information
3. **Context Retrieval:** Answers reference specific document content
4. **Agent Integration:** Multi-turn conversations work correctly
5. **Performance:** Responses generated within reasonable time limits

## 🐛 Troubleshooting

### Common Issues

**1. Authentication Failed**
```
❌ Authentication failed: 401 Unauthorized
```
- Check if the login credentials are correct
- Verify the user exists in the database
- Ensure the server is running

**2. PDF File Not Found**
```
❌ PDF file not found: /path/to/Coochie_Information pack.pdf
```
- Verify the file path is correct
- Check file permissions
- Ensure the file exists in the specified location

**3. Server Not Running**
```
❌ Server not accessible: Connection refused
```
- Start the FastAPI server: `python start_server.py`
- Check if port 8000 is available
- Verify no firewall blocking the connection

**4. Processing Timeout**
```
⚠️ Processing timeout reached - continuing with tests
```
- This is normal for large PDFs
- Check server logs for processing status
- Increase timeout in test configuration if needed

**5. Empty or Poor Quality Answers**
```
❌ No expected keywords found in answer
```
- Check if document processing completed successfully
- Verify vector embeddings were generated
- Check DocQA system health endpoint

### Debug Mode
For detailed debugging, check the server logs while running tests:

```bash
# In one terminal, start server with debug logging
python start_server.py

# In another terminal, run tests
python run_coochie_rag_tests.py simple
```

## 📈 Performance Expectations

### Processing Times
- **PDF Upload:** 5-15 seconds
- **Document Processing:** 30-120 seconds (depending on PDF size)
- **Question Answering:** 2-10 seconds per question
- **Agent Responses:** 3-15 seconds per interaction

### Success Criteria
- **Document Upload:** 100% success rate
- **Question Answering:** >80% relevant responses
- **Agent Integration:** >90% successful interactions
- **Overall System:** >85% test pass rate

## 🔄 Continuous Testing

### Automated Testing
Add to your CI/CD pipeline:

```bash
# Quick validation in CI
python run_coochie_rag_tests.py quick

# Full validation for releases
python run_coochie_rag_tests.py comprehensive
```

### Regular Monitoring
Run tests regularly to ensure system health:

```bash
# Daily health check
python run_coochie_rag_tests.py quick

# Weekly comprehensive test
python run_coochie_rag_tests.py comprehensive
```

## 📞 Support

If tests fail consistently:

1. Check server logs for detailed error messages
2. Verify database connectivity and health
3. Ensure all required services (Redis, PostgreSQL, etc.) are running
4. Check OpenAI API key and rate limits
5. Verify document processing pipeline is functioning

For additional help, check the main project documentation or contact the development team.
