#!/usr/bin/env python3
"""
Test Enhanced Processing with Coochie Information Pack

This script tests the enhanced document processing system specifically
with the Coochie Information Pack PDF to validate improved capabilities.
"""

import asyncio
import os
import sys
import time
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configuration
PDF_PATH = "Coochie_Information pack.pdf"
BASE_URL = "http://localhost:8000/api"

async def test_basic_processing():
    """Test basic enhanced processing without full system"""
    print("🧪 Testing Basic Enhanced Processing...")
    
    try:
        # Import the enhanced processor directly
        from docqa.advanced_ingestion.layout_aware_processor import LayoutAwareProcessor, AdvancedConfig
        
        # Create configuration
        config = AdvancedConfig(
            use_layoutparser=False,  # Disable for basic test
            use_tesseract=True,
            use_easyocr=False,      # Disable for basic test
            use_gpt4_vision=True,
            parallel_processing=False,
            timeout_seconds=120
        )
        
        # Initialize processor
        processor = LayoutAwareProcessor(
            openai_api_key=os.getenv('OPENAI_API_KEY', ''),
            config=config
        )
        
        if not os.path.exists(PDF_PATH):
            print(f"❌ PDF file not found: {PDF_PATH}")
            return False
        
        print(f"📄 Processing: {PDF_PATH}")
        start_time = time.time()
        
        # Process document
        result = await processor.process_document(Path(PDF_PATH))
        
        processing_time = time.time() - start_time
        
        print("✅ Basic enhanced processing completed!")
        print(f"   Processing time: {processing_time:.2f}s")
        print(f"   Text length: {len(result.structured_text)} characters")
        print(f"   Layout elements: {len(result.layout_elements)}")
        print(f"   Images: {len(result.images)}")
        print(f"   Tables: {len(result.tables)}")
        print(f"   Charts: {len(result.charts)}")
        print(f"   Semantic chunks: {len(result.semantic_chunks)}")
        
        # Show sample content
        if result.structured_text:
            print(f"\n📝 Sample content (first 300 chars):")
            print(result.structured_text[:300] + "...")
        
        # Test specific franchise-related content extraction
        franchise_content = extract_franchise_info(result.structured_text)
        if franchise_content:
            print(f"\n💼 Franchise information detected:")
            for key, value in franchise_content.items():
                print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic enhanced processing failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def extract_franchise_info(text: str) -> dict:
    """Extract franchise-specific information from text"""
    import re
    
    franchise_info = {}
    text_lower = text.lower()
    
    # Look for investment information
    investment_patterns = [
        r'investment.*?\$[\d,]+',
        r'franchise fee.*?\$[\d,]+',
        r'total investment.*?\$[\d,]+',
        r'initial investment.*?\$[\d,]+',
        r'cost.*?\$[\d,]+'
    ]
    
    for pattern in investment_patterns:
        matches = re.findall(pattern, text_lower)
        if matches:
            franchise_info['investment_info'] = matches[:3]  # First 3 matches
            break
    
    # Look for support information
    support_keywords = ['training', 'support', 'assistance', 'help', 'guidance']
    support_found = [keyword for keyword in support_keywords if keyword in text_lower]
    if support_found:
        franchise_info['support_mentioned'] = support_found
    
    # Look for territory information
    territory_keywords = ['territory', 'location', 'area', 'region', 'exclusive']
    territory_found = [keyword for keyword in territory_keywords if keyword in text_lower]
    if territory_found:
        franchise_info['territory_mentioned'] = territory_found
    
    # Look for business model information
    business_keywords = ['car wash', 'hydrogreen', 'eco-friendly', 'environmental']
    business_found = [keyword for keyword in business_keywords if keyword in text_lower]
    if business_found:
        franchise_info['business_type'] = business_found
    
    return franchise_info

async def test_question_answering():
    """Test question answering with enhanced processing"""
    print("\n❓ Testing Enhanced Question Answering...")
    
    try:
        # This would require the full system to be running
        # For now, just validate that we can process the document
        
        test_questions = [
            "What are the investment requirements for Coochie Hydrogreen franchise?",
            "What franchise fees does Coochie Hydrogreen charge?",
            "What support does Coochie Hydrogreen provide to franchisees?",
            "What are the territory requirements?",
            "Is Coochie Hydrogreen an eco-friendly business?"
        ]
        
        print("📋 Test questions prepared:")
        for i, question in enumerate(test_questions, 1):
            print(f"   {i}. {question}")
        
        print("\n💡 To test question answering:")
        print("   1. Start the server: python start_server.py")
        print("   2. Run the RAG test: python test_coochie_rag_simple.py")
        print("   3. The enhanced processing should provide better answers")
        
        return True
        
    except Exception as e:
        print(f"❌ Question answering test setup failed: {str(e)}")
        return False

def check_prerequisites():
    """Check if prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check OpenAI API key
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY environment variable not set")
        return False
    
    print("✅ OpenAI API key found")
    
    # Check PDF file
    if not os.path.exists(PDF_PATH):
        print(f"❌ PDF file not found: {PDF_PATH}")
        return False
    
    print(f"✅ PDF file found: {PDF_PATH}")
    
    # Check basic imports
    try:
        import fitz
        import PIL
        print("✅ Basic processing libraries available")
    except ImportError as e:
        print(f"❌ Missing basic libraries: {e}")
        return False
    
    return True

def show_enhancement_benefits():
    """Show the benefits of enhanced processing"""
    benefits = """
🚀 ENHANCED DOCUMENT PROCESSING BENEFITS

The new enhanced processing system provides:

1. **Layout-Aware Understanding**
   - Detects titles, headings, paragraphs, lists
   - Preserves document structure and reading order
   - Better context understanding

2. **Advanced Text Extraction**
   - Multiple OCR engines (Tesseract, EasyOCR)
   - Better handling of scanned PDFs
   - Enhanced image preprocessing

3. **Multi-Modal Content Analysis**
   - AI-powered chart and image analysis using GPT-4 Vision
   - Table detection and extraction
   - Cross-referencing between text and visual elements

4. **Intelligent Chunking**
   - Semantic chunking that preserves context
   - Layout-aware chunk boundaries
   - Enhanced metadata for better retrieval

5. **Franchise-Specific Enhancements**
   - Detects financial information (fees, investments)
   - Identifies support and training mentions
   - Extracts territory and location requirements
   - Recognizes business model information

6. **Better Question Answering**
   - More accurate context retrieval
   - Enhanced understanding of document structure
   - Better handling of complex queries
   - Improved relevance scoring

This should significantly improve the system's ability to answer
questions about franchise documents like the Coochie Information Pack.
"""
    
    print(benefits)

async def main():
    """Main test function"""
    print("🧪 Enhanced Processing Test for Coochie Information Pack")
    print("=" * 60)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n💡 Setup instructions:")
        print("1. Set OPENAI_API_KEY environment variable")
        print("2. Ensure Coochie_Information pack.pdf is in the current directory")
        print("3. Install required packages: pip install -r requirements_enhanced_processing.txt")
        sys.exit(1)
    
    # Show benefits
    show_enhancement_benefits()
    
    # Test basic processing
    basic_success = await test_basic_processing()
    
    # Test question answering setup
    qa_success = await test_question_answering()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    print(f"Basic Enhanced Processing: {'✅ PASSED' if basic_success else '❌ FAILED'}")
    print(f"Question Answering Setup: {'✅ PASSED' if qa_success else '❌ FAILED'}")
    
    if basic_success and qa_success:
        print("\n🎉 Enhanced processing is working correctly!")
        print("\n🚀 Next steps:")
        print("1. Integrate enhanced processing into your application")
        print("2. Test with the full RAG system")
        print("3. Compare results with the original system")
        print("\n💡 Integration guide:")
        print("   Run: python upgrade_document_processing.py")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
