"""
Document Tools for Agent System
Tools for document processing, text extraction, and search
"""

from typing import Dict, Any, Optional
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field
import structlog
import uuid

logger = structlog.get_logger()


class IngestDocumentInput(BaseModel):
    """Input schema for document ingestion"""
    source: str = Field(description="Document source (file path, URL, or content)")
    document_type: str = Field("general", description="Type of document")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")
    franchisor_id: Optional[str] = Field(None, description="Associated franchisor ID")


class IngestDocumentTool(BaseTool):
    """Tool for ingesting documents into the knowledge base"""

    name: str = "ingest_document"
    description: str = "Ingest a document into the knowledge base for question answering"
    args_schema: type = IngestDocumentInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Ingest document"""
        try:
            # This would integrate with the existing DocQA system
            from app.services.docqa_integration_service import DocQAIntegrationService
            
            docqa_service = DocQAIntegrationService()
            
            # Process document based on source type
            source = kwargs["source"]
            if source.startswith(("http://", "https://", "s3://")):
                # URL or S3 path
                result = await docqa_service.process_document_async(
                    document_id=str(uuid.uuid4()),
                    file_url=source
                )
            else:
                # Local file path
                result = await docqa_service.process_document_async(
                    document_id=str(uuid.uuid4()),
                    file_url=source
                )
            
            if result and result.get("success"):
                return f"Document ingested successfully. Document ID: {result.get('document_id', 'unknown')}"
            else:
                return f"Failed to ingest document: {result.get('error', 'Unknown error')}"
                
        except Exception as e:
            logger.error(f"Error ingesting document: {str(e)}")
            return f"Error ingesting document: {str(e)}"


class SearchDocumentsInput(BaseModel):
    """Input schema for document search"""
    query: str = Field(description="Search query")
    top_k: int = Field(5, description="Number of results to return")
    similarity_threshold: float = Field(0.7, description="Minimum similarity threshold")
    document_type: Optional[str] = Field(None, description="Filter by document type")
    franchisor_id: Optional[str] = Field(None, description="Filter by franchisor")


class SearchDocumentsTool(BaseTool):
    """Tool for searching documents in the knowledge base"""

    name: str = "search_documents"
    description: str = "Search for relevant documents based on a query"
    args_schema: type = SearchDocumentsInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))
    
    async def _arun(self, **kwargs) -> str:
        """Search documents using DocQA system"""
        try:
            from docqa.central_api import ask_question

            query = kwargs["query"]
            top_k = kwargs.get("top_k", 5)
            similarity_threshold = kwargs.get("similarity_threshold", 0.7)
            franchisor_id = kwargs.get("franchisor_id")

            logger.info(f"Searching documents with query: {query[:100]}")

            # Use DocQA to search for relevant content
            results = ask_question(
                question=query,
                top_k=top_k,
                similarity_threshold=similarity_threshold,
                include_metadata=True,
                franchisor_id=franchisor_id
            )

            if results and hasattr(results, 'sources') and results.sources:
                # Format the search results for the agent
                formatted_results = []
                for source in results.sources:
                    formatted_results.append({
                        "content": source.get("content", ""),
                        "source": source.get("source", "Unknown"),
                        "similarity": source.get("similarity_score", 0.0),
                        "metadata": source.get("metadata", {})
                    })

                # Return formatted results as a string that the agent can parse
                return f"Found {len(formatted_results)} relevant documents:\n\n" + "\n\n".join([
                    f"Source: {result['source']}\nSimilarity: {result['similarity']:.2f}\nContent: {result['content'][:500]}..."
                    for result in formatted_results[:3]  # Limit to top 3 for context
                ])
            else:
                return "No relevant documents found for the query."

        except Exception as e:
            logger.error(f"Error searching documents: {str(e)}")
            return f"Error searching documents: {str(e)}"


class ExtractTextInput(BaseModel):
    """Input schema for text extraction"""
    file_path: str = Field(description="Path to the file")
    extract_images: bool = Field(False, description="Whether to extract text from images (OCR)")


class ExtractTextTool(BaseTool):
    """Tool for extracting text from various file formats"""

    name: str = "extract_text"
    description: str = "Extract text content from documents (PDF, DOC, images, etc.)"
    args_schema: type = ExtractTextInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Extract text from file"""
        try:
            file_path = kwargs["file_path"]
            extract_images = kwargs.get("extract_images", False)
            
            # Only PDF files are supported for document ingestion
            import os
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension == '.pdf':
                return await self._extract_from_pdf(file_path, extract_images)
            else:
                return f"Only PDF files are supported for document ingestion. Provided: {file_extension}"
                
        except Exception as e:
            logger.error(f"Error extracting text: {str(e)}")
            return f"Error extracting text: {str(e)}"
    
    async def _extract_from_pdf(self, file_path: str, extract_images: bool = False) -> str:
        """Extract text from PDF"""
        try:
            import PyPDF2
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                
                return text.strip()
                
        except ImportError:
            return "PyPDF2 not installed. Cannot extract PDF text."
        except Exception as e:
            return f"Error extracting PDF text: {str(e)}"
    
    async def _extract_from_word(self, file_path: str) -> str:
        """Extract text from Word documents"""
        try:
            import docx
            
            doc = docx.Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text.strip()
            
        except ImportError:
            return "python-docx not installed. Cannot extract Word document text."
        except Exception as e:
            return f"Error extracting Word document text: {str(e)}"
    
    async def _extract_from_text(self, file_path: str) -> str:
        """Extract text from text files"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
                
        except Exception as e:
            return f"Error reading text file: {str(e)}"
    
    async def _extract_from_image(self, file_path: str) -> str:
        """Extract text from images using OCR"""
        try:
            import pytesseract
            from PIL import Image
            
            image = Image.open(file_path)
            text = pytesseract.image_to_string(image)
            
            return text.strip()
            
        except ImportError:
            return "pytesseract or PIL not installed. Cannot perform OCR."
        except Exception as e:
            return f"Error performing OCR: {str(e)}"


class SummarizeDocumentInput(BaseModel):
    """Input schema for document summarization"""
    document_id: str = Field(description="Document ID to summarize")
    max_length: int = Field(500, description="Maximum length of summary")


class SummarizeDocumentTool(BaseTool):
    """Tool for summarizing documents"""

    name: str = "summarize_document"
    description: str = "Generate a summary of a document"
    args_schema: type = SummarizeDocumentInput

    def _run(self, **kwargs) -> str:
        """Sync version"""
        import asyncio
        return asyncio.run(self._arun(**kwargs))

    async def _arun(self, **kwargs) -> str:
        """Summarize document"""
        try:
            # This would retrieve document content and generate summary
            document_id = kwargs["document_id"]
            max_length = kwargs.get("max_length", 500)
            
            # Mock implementation
            return f"Summary of document {document_id} (max {max_length} chars): This document contains franchise information including investment requirements, territory details, and support structure."
                
        except Exception as e:
            logger.error(f"Error summarizing document: {str(e)}")
            return f"Error summarizing document: {str(e)}"
