#!/usr/bin/env python3
"""
Coochie RAG Test Runner

This script provides options to run different levels of RAG testing:
1. Simple test - Basic functionality
2. Comprehensive test - Full system validation
3. Quick validation - Just check if system is working

Usage:
    python run_coochie_rag_tests.py simple      # Run simple test
    python run_coochie_rag_tests.py comprehensive  # Run comprehensive test
    python run_coochie_rag_tests.py quick       # Run quick validation
    python run_coochie_rag_tests.py            # Interactive menu
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# Configuration
PDF_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"
BASE_URL = "http://localhost:8000"

def check_prerequisites() -> bool:
    """Check if all prerequisites are met"""
    print("🔍 Checking prerequisites...")
    
    # Check if PDF file exists
    if not os.path.exists(PDF_PATH):
        print(f"❌ PDF file not found: {PDF_PATH}")
        print("Please ensure the Coochie Information pack.pdf file is in the correct location.")
        return False
    
    print(f"✅ PDF file found: {os.path.basename(PDF_PATH)}")
    
    # Check if server is running
    try:
        import requests
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Server is running")
        else:
            print(f"⚠️ Server responded with status: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {str(e)}")
        print("Please ensure the FastAPI server is running on http://localhost:8000")
        return False
    
    return True

def run_simple_test() -> bool:
    """Run the simple RAG test"""
    print("\n🧪 Running Simple RAG Test...")
    print("="*50)
    
    try:
        result = subprocess.run([
            sys.executable, "test_coochie_rag_simple.py"
        ], capture_output=False, text=True)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running simple test: {str(e)}")
        return False

def run_comprehensive_test() -> bool:
    """Run the comprehensive RAG test"""
    print("\n🧪 Running Comprehensive RAG Test...")
    print("="*50)
    
    try:
        result = subprocess.run([
            sys.executable, "test_coochie_rag_comprehensive.py"
        ], capture_output=False, text=True)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running comprehensive test: {str(e)}")
        return False

def run_quick_validation() -> bool:
    """Run a quick validation test"""
    print("\n🧪 Running Quick Validation...")
    print("="*50)
    
    try:
        import requests
        
        # Quick login test
        print("🔐 Testing authentication...")
        login_data = {
            "email_or_mobile": "<EMAIL>",
            "password": "Admin@1234",
            "remember_me": True
        }
        
        response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if response.status_code != 200:
            print("❌ Authentication failed")
            return False
        
        result = response.json()
        if not result.get("success"):
            print("❌ Authentication failed")
            return False
        
        token = result["data"]["details"]["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        print("✅ Authentication successful")
        
        # Quick health check
        print("🏥 Testing system health...")
        response = requests.get(f"{BASE_URL}/api/docqa/health", headers=headers)
        if response.status_code == 200:
            print("✅ DocQA system healthy")
        else:
            print("⚠️ DocQA system may have issues")
        
        # Quick agent status check
        print("🤖 Testing agent system...")
        response = requests.get(f"{BASE_URL}/api/agents/status", headers=headers)
        if response.status_code == 200:
            print("✅ Agent system healthy")
        else:
            print("⚠️ Agent system may have issues")
        
        print("\n✅ Quick validation completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Quick validation error: {str(e)}")
        return False

def show_menu():
    """Show interactive menu"""
    print("\n🎯 Coochie RAG Test Menu")
    print("="*30)
    print("1. Quick Validation (30 seconds)")
    print("2. Simple Test (5-10 minutes)")
    print("3. Comprehensive Test (15-20 minutes)")
    print("4. Exit")
    print()
    
    while True:
        try:
            choice = input("Select an option (1-4): ").strip()
            
            if choice == "1":
                return "quick"
            elif choice == "2":
                return "simple"
            elif choice == "3":
                return "comprehensive"
            elif choice == "4":
                return "exit"
            else:
                print("Invalid choice. Please select 1-4.")
        except KeyboardInterrupt:
            return "exit"

def main():
    """Main function"""
    print("🚀 Coochie RAG Test Runner")
    print("="*50)
    print("This script tests the RAG and AI functionality using the Coochie Information Pack PDF")
    print()
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites not met. Please fix the issues above and try again.")
        return False
    
    # Determine test type
    test_type = None
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
    else:
        test_type = show_menu()
    
    if test_type == "exit":
        print("👋 Goodbye!")
        return True
    
    # Run the selected test
    success = False
    start_time = time.time()
    
    if test_type == "quick":
        success = run_quick_validation()
    elif test_type == "simple":
        success = run_simple_test()
    elif test_type == "comprehensive":
        success = run_comprehensive_test()
    else:
        print(f"❌ Unknown test type: {test_type}")
        print("Available options: quick, simple, comprehensive")
        return False
    
    # Show results
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS")
    print("="*50)
    
    if success:
        print("🎉 TEST PASSED!")
        print("The RAG and AI functionality is working correctly with the Coochie Information Pack.")
    else:
        print("❌ TEST FAILED!")
        print("Some issues were detected. Check the output above for details.")
    
    print(f"⏱️ Duration: {duration:.1f} seconds")
    print(f"📄 PDF File: {os.path.basename(PDF_PATH)}")
    
    if test_type in ["simple", "comprehensive"]:
        print("\n💡 Next Steps:")
        print("   - Check server logs for detailed AI responses")
        print("   - Verify document processing in the database")
        print("   - Test with additional questions via the API")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {str(e)}")
        sys.exit(1)
